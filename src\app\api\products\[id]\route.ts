import { NextRequest } from "next/server";
import prisma from "@/db/client";
import { productSchema } from "@/lib/validations";
import { PERMISSIONS } from "@/config/constants";
import {
  withAuth,
  successResponse,
  errorResponse,
  validateRequest,
  logApiRequest,
} from "@/lib/api-utils";

// GET /api/products/[id] - Get single product
export const GET = withAuth(
  async (req: NextRequest, { params }: { params: { id: string } }, user: any) => {
    logApiRequest(req, user, "GET_PRODUCT");

    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        warehouse: true,
        inventoryTransactions: {
          orderBy: { createdAt: "desc" },
          take: 10,
          include: {
            user: {
              select: { name: true, email: true },
            },
          },
        },
        orderItems: {
          include: {
            order: {
              select: {
                id: true,
                createdAt: true,
                customer: {
                  select: { name: true },
                },
              },
            },
          },
          orderBy: { order: { createdAt: "desc" } },
          take: 10,
        },
        _count: {
          select: {
            orderItems: true,
          },
        },
      },
    });

    if (!product) {
      return errorResponse("Product not found", 404);
    }

    // Calculate sales statistics
    const totalSold = product.orderItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalRevenue = product.orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    const productWithStats = {
      ...product,
      stats: {
        totalSold,
        totalRevenue,
        averageOrderQuantity: product.orderItems.length > 0 ? totalSold / product.orderItems.length : 0,
        turnoverRate: product.stock > 0 ? totalSold / (product.stock + totalSold) : 0,
      },
    };

    return successResponse(productWithStats);
  },
  [PERMISSIONS.PRODUCT_READ]
);

// PUT /api/products/[id] - Update product
export const PUT = withAuth(
  async (req: NextRequest, { params }: { params: { id: string } }, user: any) => {
    logApiRequest(req, user, "UPDATE_PRODUCT");

    const validatedData = await validateRequest(req, productSchema.partial());

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id },
    });

    if (!existingProduct) {
      return errorResponse("Product not found", 404);
    }

    // Check if category and warehouse exist (if being updated)
    if (validatedData.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: validatedData.categoryId },
      });
      if (!category) {
        throw new Error("Category not found");
      }
    }

    if (validatedData.warehouseId) {
      const warehouse = await prisma.warehouse.findUnique({
        where: { id: validatedData.warehouseId },
      });
      if (!warehouse) {
        throw new Error("Warehouse not found");
      }
    }

    // Track stock changes
    const stockChange = validatedData.stock !== undefined 
      ? validatedData.stock - existingProduct.stock 
      : 0;

    // Update product
    const updatedProduct = await prisma.product.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        category: true,
        warehouse: true,
      },
    });

    // Create inventory transaction if stock changed
    if (stockChange !== 0) {
      await prisma.inventoryTransaction.create({
        data: {
          productId: params.id,
          type: stockChange > 0 ? "ADJUSTMENT" : "ADJUSTMENT",
          quantity: Math.abs(stockChange),
          reason: `Stock ${stockChange > 0 ? "increase" : "decrease"} via product update`,
          userId: user.id,
        },
      });
    }

    // Create notification for significant changes
    const significantChanges = [];
    if (validatedData.price && validatedData.price !== existingProduct.price) {
      significantChanges.push(`Price: ${existingProduct.price} → ${validatedData.price}`);
    }
    if (stockChange !== 0) {
      significantChanges.push(`Stock: ${existingProduct.stock} → ${updatedProduct.stock}`);
    }

    if (significantChanges.length > 0) {
      await prisma.notification.create({
        data: {
          title: "Product Updated",
          message: `Product "${updatedProduct.name}" has been updated. Changes: ${significantChanges.join(", ")}`,
          type: "INFO",
          priority: "LOW",
          metadata: {
            productId: updatedProduct.id,
            productName: updatedProduct.name,
            changes: significantChanges,
            updatedBy: user.name,
          },
        },
      });
    }

    return successResponse(updatedProduct, "Product updated successfully");
  },
  [PERMISSIONS.PRODUCT_UPDATE]
);

// DELETE /api/products/[id] - Delete product
export const DELETE = withAuth(
  async (req: NextRequest, { params }: { params: { id: string } }, user: any) => {
    logApiRequest(req, user, "DELETE_PRODUCT");

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        orderItems: true,
      },
    });

    if (!product) {
      return errorResponse("Product not found", 404);
    }

    // Check if product has orders
    if (product.orderItems.length > 0) {
      throw new Error("Cannot delete product with existing orders");
    }

    // Delete product (this will cascade delete related records)
    await prisma.product.delete({
      where: { id: params.id },
    });

    // Create notification
    await prisma.notification.create({
      data: {
        title: "Product Deleted",
        message: `Product "${product.name}" has been deleted`,
        type: "WARNING",
        priority: "MEDIUM",
        metadata: {
          productId: product.id,
          productName: product.name,
          deletedBy: user.name,
        },
      },
    });

    return successResponse(
      { id: params.id },
      "Product deleted successfully"
    );
  },
  [PERMISSIONS.PRODUCT_DELETE]
);
