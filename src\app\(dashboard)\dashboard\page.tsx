"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Stats,
  Table,
  Badge,
  Button,
  LoadingSpinner,
} from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";

interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  revenueChange: number;
  ordersChange: number;
  customersChange: number;
  productsChange: number;
}

interface RecentOrder {
  id: string;
  customerName: string;
  total: number;
  status: string;
  createdAt: string;
}

interface LowStockProduct {
  id: string;
  name: string;
  stock: number;
  category: string;
}

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [lowStockProducts, setLowStockProducts] = useState<LowStockProduct[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Simulate API calls - replace with actual API endpoints
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API responses
      setStats({
        totalRevenue: 125430,
        totalOrders: 1234,
        totalCustomers: 567,
        totalProducts: 89,
        revenueChange: 12.5,
        ordersChange: 8.2,
        customersChange: -2.1,
        productsChange: 5.7,
      });

      setRecentOrders([
        {
          id: "ORD-001",
          customerName: "Toko Berkah Jaya",
          total: 2500000,
          status: "PROCESSING",
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "ORD-002",
          customerName: "Warung Maju Mundur",
          total: 1750000,
          status: "SHIPPED",
          createdAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "ORD-003",
          customerName: "Toko Sumber Rejeki",
          total: 3200000,
          status: "DELIVERED",
          createdAt: "2024-01-14T16:45:00Z",
        },
      ]);

      setLowStockProducts([
        {
          id: "PRD-001",
          name: "Indomie Goreng",
          stock: 5,
          category: "Makanan Instan",
        },
        {
          id: "PRD-002",
          name: "Teh Botol Sosro",
          stock: 8,
          category: "Minuman",
        },
        {
          id: "PRD-003",
          name: "Royco Kaldu Ayam",
          stock: 3,
          category: "Bumbu Dapur",
        },
      ]);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "warning" as const, label: "Pending" },
      PROCESSING: { variant: "info" as const, label: "Processing" },
      SHIPPED: { variant: "primary" as const, label: "Shipped" },
      DELIVERED: { variant: "success" as const, label: "Delivered" },
      CANCELLED: { variant: "error" as const, label: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "default" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const orderColumns = [
    {
      key: "id",
      title: "Order ID",
      dataIndex: "id",
    },
    {
      key: "customerName",
      title: "Customer",
      dataIndex: "customerName",
    },
    {
      key: "total",
      title: "Total",
      dataIndex: "total",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "status",
      title: "Status",
      dataIndex: "status",
      render: (value: string) => getStatusBadge(value),
    },
    {
      key: "createdAt",
      title: "Date",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
    },
  ];

  const productColumns = [
    {
      key: "name",
      title: "Product",
      dataIndex: "name",
    },
    {
      key: "category",
      title: "Category",
      dataIndex: "category",
    },
    {
      key: "stock",
      title: "Stock",
      dataIndex: "stock",
      render: (value: number) => (
        <Badge variant={value <= 5 ? "error" : "warning"}>
          {value} units
        </Badge>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  const statsData = stats ? [
    {
      label: "Total Revenue",
      value: formatCurrency(stats.totalRevenue),
      change: {
        value: Math.abs(stats.revenueChange),
        type: stats.revenueChange >= 0 ? "increase" : "decrease",
        period: "from last month",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: "success" as const,
    },
    {
      label: "Total Orders",
      value: stats.totalOrders.toLocaleString(),
      change: {
        value: Math.abs(stats.ordersChange),
        type: stats.ordersChange >= 0 ? "increase" : "decrease",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Total Customers",
      value: stats.totalCustomers.toLocaleString(),
      change: {
        value: Math.abs(stats.customersChange),
        type: stats.customersChange >= 0 ? "increase" : "decrease",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: stats.customersChange >= 0 ? "info" : "warning",
    },
    {
      label: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      change: {
        value: Math.abs(stats.productsChange),
        type: stats.productsChange >= 0 ? "increase" : "decrease",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: "info" as const,
    },
  ] : [];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-secondary-600">
          Here's what's happening with your business today.
        </p>
      </div>

      {/* Statistics */}
      <Stats stats={statsData} columns={4} />

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <Card variant="elevated">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Orders</CardTitle>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Table
              columns={orderColumns}
              data={recentOrders}
              emptyText="No recent orders"
            />
          </CardContent>
        </Card>

        {/* Low Stock Alert */}
        <Card variant="elevated">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Low Stock Alert</CardTitle>
              <Badge variant="error">{lowStockProducts.length}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <Table
              columns={productColumns}
              data={lowStockProducts}
              emptyText="All products are well stocked"
            />
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card variant="elevated">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Product
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              New Order
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              Add Customer
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              View Reports
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardPage;
