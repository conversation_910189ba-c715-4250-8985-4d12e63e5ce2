"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>Header,
  CardTitle,
  CardContent,
  Button,
  Select,
  LoadingSpinner,
  Table,
  Badge,
} from "@/components/ui";
import { formatCurrency, formatDate, formatPercentage } from "@/lib/utils";

interface InventoryReportsProps {
  period: string;
  warehouse: string;
}

interface ReportData {
  stockValuation: {
    totalValue: number;
    byCategory: Array<{
      category: string;
      value: number;
      percentage: number;
    }>;
    byWarehouse: Array<{
      warehouse: string;
      value: number;
      percentage: number;
    }>;
  };
  turnoverAnalysis: {
    averageTurnover: number;
    fastMoving: Array<{
      productName: string;
      turnoverRate: number;
      revenue: number;
    }>;
    slowMoving: Array<{
      productName: string;
      turnoverRate: number;
      daysInStock: number;
    }>;
  };
  stockHealth: {
    healthyStock: number;
    lowStock: number;
    outOfStock: number;
    overstock: number;
  };
  trends: {
    stockIn: Array<{ date: string; quantity: number }>;
    stockOut: Array<{ date: string; quantity: number }>;
    adjustments: Array<{ date: string; quantity: number }>;
  };
}

const InventoryReports: React.FC<InventoryReportsProps> = ({
  period,
  warehouse,
}) => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState("valuation");

  useEffect(() => {
    fetchReportData();
  }, [period, warehouse]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock report data
      setReportData({
        stockValuation: {
          totalValue: 245680000,
          byCategory: [
            { category: "Makanan Instan", value: 89450000, percentage: 36.4 },
            { category: "Minuman", value: 67890000, percentage: 27.6 },
            { category: "Makanan Ringan", value: 45320000, percentage: 18.4 },
            { category: "Bahan Pokok", value: 32100000, percentage: 13.1 },
            { category: "Bumbu Dapur", value: 10920000, percentage: 4.5 },
          ],
          byWarehouse: [
            { warehouse: "Gudang Utama", value: 156780000, percentage: 63.8 },
            { warehouse: "Gudang Cabang", value: 67890000, percentage: 27.6 },
            { warehouse: "Gudang Frozen", value: 21010000, percentage: 8.6 },
          ],
        },
        turnoverAnalysis: {
          averageTurnover: 4.2,
          fastMoving: [
            { productName: "Indomie Goreng Original", turnoverRate: 12.5, revenue: 15750000 },
            { productName: "Teh Botol Sosro 450ml", turnoverRate: 10.8, revenue: 12300000 },
            { productName: "Chitato Sapi Panggang", turnoverRate: 8.9, revenue: 8950000 },
          ],
          slowMoving: [
            { productName: "Bumbu Rendang Instant", turnoverRate: 0.8, daysInStock: 145 },
            { productName: "Kerupuk Udang Premium", turnoverRate: 1.2, daysInStock: 98 },
            { productName: "Sambal Oelek Botol", turnoverRate: 1.5, daysInStock: 76 },
          ],
        },
        stockHealth: {
          healthyStock: 125,
          lowStock: 23,
          outOfStock: 8,
          overstock: 12,
        },
        trends: {
          stockIn: [
            { date: "2024-01-01", quantity: 1250 },
            { date: "2024-01-08", quantity: 1890 },
            { date: "2024-01-15", quantity: 2100 },
          ],
          stockOut: [
            { date: "2024-01-01", quantity: 980 },
            { date: "2024-01-08", quantity: 1450 },
            { date: "2024-01-15", quantity: 1680 },
          ],
          adjustments: [
            { date: "2024-01-01", quantity: -45 },
            { date: "2024-01-08", quantity: -23 },
            { date: "2024-01-15", quantity: -67 },
          ],
        },
      });
    } catch (error) {
      console.error("Error fetching report data:", error);
    } finally {
      setLoading(false);
    }
  };

  const reportOptions = [
    { value: "valuation", label: "Stock Valuation" },
    { value: "turnover", label: "Turnover Analysis" },
    { value: "health", label: "Stock Health" },
    { value: "trends", label: "Trends Analysis" },
  ];

  const categoryColumns = [
    {
      key: "category",
      title: "Category",
      dataIndex: "category",
    },
    {
      key: "value",
      title: "Value",
      dataIndex: "value",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "percentage",
      title: "Percentage",
      dataIndex: "percentage",
      render: (value: number) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-secondary-200 rounded-full h-2">
            <div
              className="h-2 bg-primary-500 rounded-full"
              style={{ width: `${value}%` }}
            />
          </div>
          <span>{value}%</span>
        </div>
      ),
    },
  ];

  const warehouseColumns = [
    {
      key: "warehouse",
      title: "Warehouse",
      dataIndex: "warehouse",
    },
    {
      key: "value",
      title: "Value",
      dataIndex: "value",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "percentage",
      title: "Percentage",
      dataIndex: "percentage",
      render: (value: number) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-secondary-200 rounded-full h-2">
            <div
              className="h-2 bg-success-500 rounded-full"
              style={{ width: `${value}%` }}
            />
          </div>
          <span>{value}%</span>
        </div>
      ),
    },
  ];

  const fastMovingColumns = [
    {
      key: "productName",
      title: "Product",
      dataIndex: "productName",
    },
    {
      key: "turnoverRate",
      title: "Turnover Rate",
      dataIndex: "turnoverRate",
      render: (value: number) => `${value}x/month`,
    },
    {
      key: "revenue",
      title: "Revenue",
      dataIndex: "revenue",
      render: (value: number) => formatCurrency(value),
    },
  ];

  const slowMovingColumns = [
    {
      key: "productName",
      title: "Product",
      dataIndex: "productName",
    },
    {
      key: "turnoverRate",
      title: "Turnover Rate",
      dataIndex: "turnoverRate",
      render: (value: number) => `${value}x/month`,
    },
    {
      key: "daysInStock",
      title: "Days in Stock",
      dataIndex: "daysInStock",
      render: (value: number) => `${value} days`,
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" text="Generating reports..." />
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="text-center py-12">
        <p className="text-secondary-600">Failed to load report data</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Report Selection */}
      <Card>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select
                options={reportOptions}
                value={selectedReport}
                onChange={setSelectedReport}
                className="w-48"
              />
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">
                Export PDF
              </Button>
              <Button variant="outline">
                Export Excel
              </Button>
              <Button variant="outline">
                Schedule Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stock Valuation Report */}
      {selectedReport === "valuation" && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Stock Valuation Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-6">
                <p className="text-sm text-secondary-600">Total Inventory Value</p>
                <p className="text-4xl font-bold text-primary-600">
                  {formatCurrency(reportData.stockValuation.totalValue)}
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-secondary-900 mb-4">By Category</h3>
                  <Table
                    columns={categoryColumns}
                    data={reportData.stockValuation.byCategory}
                    emptyText="No category data available"
                  />
                </div>
                
                <div>
                  <h3 className="font-semibold text-secondary-900 mb-4">By Warehouse</h3>
                  <Table
                    columns={warehouseColumns}
                    data={reportData.stockValuation.byWarehouse}
                    emptyText="No warehouse data available"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Turnover Analysis Report */}
      {selectedReport === "turnover" && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Turnover Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-6">
                <p className="text-sm text-secondary-600">Average Turnover Rate</p>
                <p className="text-4xl font-bold text-success-600">
                  {reportData.turnoverAnalysis.averageTurnover}x
                </p>
                <p className="text-sm text-secondary-500">per month</p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-secondary-900 mb-4">Fast Moving Products</h3>
                  <Table
                    columns={fastMovingColumns}
                    data={reportData.turnoverAnalysis.fastMoving}
                    emptyText="No fast moving products"
                  />
                </div>
                
                <div>
                  <h3 className="font-semibold text-secondary-900 mb-4">Slow Moving Products</h3>
                  <Table
                    columns={slowMovingColumns}
                    data={reportData.turnoverAnalysis.slowMoving}
                    emptyText="No slow moving products"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Stock Health Report */}
      {selectedReport === "health" && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Stock Health Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-success-50 rounded-lg">
                  <div className="text-3xl font-bold text-success-600">
                    {reportData.stockHealth.healthyStock}
                  </div>
                  <div className="text-sm text-success-700">Healthy Stock</div>
                  <div className="text-xs text-success-600">
                    {Math.round((reportData.stockHealth.healthyStock / 
                      (reportData.stockHealth.healthyStock + reportData.stockHealth.lowStock + 
                       reportData.stockHealth.outOfStock + reportData.stockHealth.overstock)) * 100)}%
                  </div>
                </div>
                
                <div className="text-center p-4 bg-warning-50 rounded-lg">
                  <div className="text-3xl font-bold text-warning-600">
                    {reportData.stockHealth.lowStock}
                  </div>
                  <div className="text-sm text-warning-700">Low Stock</div>
                  <div className="text-xs text-warning-600">
                    {Math.round((reportData.stockHealth.lowStock / 
                      (reportData.stockHealth.healthyStock + reportData.stockHealth.lowStock + 
                       reportData.stockHealth.outOfStock + reportData.stockHealth.overstock)) * 100)}%
                  </div>
                </div>
                
                <div className="text-center p-4 bg-error-50 rounded-lg">
                  <div className="text-3xl font-bold text-error-600">
                    {reportData.stockHealth.outOfStock}
                  </div>
                  <div className="text-sm text-error-700">Out of Stock</div>
                  <div className="text-xs text-error-600">
                    {Math.round((reportData.stockHealth.outOfStock / 
                      (reportData.stockHealth.healthyStock + reportData.stockHealth.lowStock + 
                       reportData.stockHealth.outOfStock + reportData.stockHealth.overstock)) * 100)}%
                  </div>
                </div>
                
                <div className="text-center p-4 bg-info-50 rounded-lg">
                  <div className="text-3xl font-bold text-info-600">
                    {reportData.stockHealth.overstock}
                  </div>
                  <div className="text-sm text-info-700">Overstock</div>
                  <div className="text-xs text-info-600">
                    {Math.round((reportData.stockHealth.overstock / 
                      (reportData.stockHealth.healthyStock + reportData.stockHealth.lowStock + 
                       reportData.stockHealth.outOfStock + reportData.stockHealth.overstock)) * 100)}%
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Trends Analysis Report */}
      {selectedReport === "trends" && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 border rounded-lg">
                  <h4 className="font-medium text-secondary-900 mb-2">Stock In Trend</h4>
                  <div className="text-2xl font-bold text-success-600">
                    {reportData.trends.stockIn.reduce((sum, item) => sum + item.quantity, 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-secondary-600">Total units received</div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <h4 className="font-medium text-secondary-900 mb-2">Stock Out Trend</h4>
                  <div className="text-2xl font-bold text-error-600">
                    {reportData.trends.stockOut.reduce((sum, item) => sum + item.quantity, 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-secondary-600">Total units sold</div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <h4 className="font-medium text-secondary-900 mb-2">Adjustments</h4>
                  <div className="text-2xl font-bold text-warning-600">
                    {Math.abs(reportData.trends.adjustments.reduce((sum, item) => sum + item.quantity, 0)).toLocaleString()}
                  </div>
                  <div className="text-sm text-secondary-600">Total adjustments</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default InventoryReports;
