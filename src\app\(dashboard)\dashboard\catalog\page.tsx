"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Badge,
  Select,
  LoadingSpinner,
  EmptyState,
  Pagination,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import ProductCard from "./components/ProductCard";
import ProductFilters from "./components/ProductFilters";
import ProductGridView from "./components/ProductGridView";
import ProductListView from "./components/ProductListView";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
  images?: string[];
  tags?: string[];
  rating?: number;
  reviewCount?: number;
  createdAt: string;
}

interface SearchFilters {
  category: string;
  warehouse: string;
  priceRange: {
    min: number;
    max: number;
  };
  stockStatus: string;
  rating: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

const CatalogPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  
  const [filters, setFilters] = useState<SearchFilters>({
    category: "ALL",
    warehouse: "ALL",
    priceRange: { min: 0, max: 1000000 },
    stockStatus: "ALL",
    rating: 0,
    sortBy: "name",
    sortOrder: "asc",
  });

  const [showFilters, setShowFilters] = useState(false);
  const [aiSearchLoading, setAiSearchLoading] = useState(false);

  const canUpdate = checkPermission(PERMISSIONS.PRODUCT_UPDATE);

  useEffect(() => {
    fetchProducts();
  }, [currentPage, itemsPerPage, searchQuery, filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API response
      const mockProducts: Product[] = [
        {
          id: "1",
          name: "Indomie Goreng Original",
          description: "Mie instan goreng rasa original yang lezat dan praktis",
          price: 3000,
          stock: 500,
          category: { id: "1", name: "Makanan Instan" },
          warehouse: { id: "1", name: "Gudang Utama" },
          images: ["/api/placeholder/300/300"],
          tags: ["mie", "instan", "goreng", "original"],
          rating: 4.5,
          reviewCount: 128,
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "2",
          name: "Teh Botol Sosro 450ml",
          description: "Minuman teh dalam kemasan botol yang menyegarkan",
          price: 4500,
          stock: 8,
          category: { id: "2", name: "Minuman" },
          warehouse: { id: "1", name: "Gudang Utama" },
          images: ["/api/placeholder/300/300"],
          tags: ["teh", "minuman", "botol", "sosro"],
          rating: 4.2,
          reviewCount: 89,
          createdAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "3",
          name: "Chitato Rasa Sapi Panggang 68g",
          description: "Keripik kentang rasa sapi panggang yang gurih dan renyah",
          price: 8500,
          stock: 150,
          category: { id: "3", name: "Makanan Ringan" },
          warehouse: { id: "1", name: "Gudang Utama" },
          images: ["/api/placeholder/300/300"],
          tags: ["keripik", "kentang", "sapi", "panggang"],
          rating: 4.7,
          reviewCount: 203,
          createdAt: "2024-01-14T16:45:00Z",
        },
        {
          id: "4",
          name: "Royco Kaldu Ayam 100g",
          description: "Bumbu kaldu ayam untuk masakan yang lebih lezat",
          price: 2500,
          stock: 300,
          category: { id: "4", name: "Bumbu Dapur" },
          warehouse: { id: "1", name: "Gudang Utama" },
          images: ["/api/placeholder/300/300"],
          tags: ["kaldu", "ayam", "bumbu", "masakan"],
          rating: 4.3,
          reviewCount: 67,
          createdAt: "2024-01-14T14:20:00Z",
        },
        {
          id: "5",
          name: "Beras Premium 5kg",
          description: "Beras berkualitas premium untuk kebutuhan sehari-hari",
          price: 75000,
          stock: 100,
          category: { id: "5", name: "Bahan Pokok" },
          warehouse: { id: "1", name: "Gudang Utama" },
          images: ["/api/placeholder/300/300"],
          tags: ["beras", "premium", "pokok", "makanan"],
          rating: 4.6,
          reviewCount: 156,
          createdAt: "2024-01-13T11:30:00Z",
        },
        {
          id: "6",
          name: "Minyak Goreng Tropical 1L",
          description: "Minyak goreng berkualitas untuk memasak sehari-hari",
          price: 18000,
          stock: 0,
          category: { id: "5", name: "Bahan Pokok" },
          warehouse: { id: "1", name: "Gudang Utama" },
          images: ["/api/placeholder/300/300"],
          tags: ["minyak", "goreng", "tropical", "masak"],
          rating: 4.1,
          reviewCount: 92,
          createdAt: "2024-01-12T08:15:00Z",
        },
      ];

      // Apply filters
      let filteredProducts = mockProducts;
      
      // Search filter
      if (searchQuery) {
        filteredProducts = filteredProducts.filter(product =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      }
      
      // Category filter
      if (filters.category !== "ALL") {
        filteredProducts = filteredProducts.filter(product => product.category.id === filters.category);
      }
      
      // Warehouse filter
      if (filters.warehouse !== "ALL") {
        filteredProducts = filteredProducts.filter(product => product.warehouse.id === filters.warehouse);
      }
      
      // Price range filter
      filteredProducts = filteredProducts.filter(product => 
        product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
      );
      
      // Stock status filter
      if (filters.stockStatus === "IN_STOCK") {
        filteredProducts = filteredProducts.filter(product => product.stock > 0);
      } else if (filters.stockStatus === "LOW_STOCK") {
        filteredProducts = filteredProducts.filter(product => product.stock > 0 && product.stock <= 10);
      } else if (filters.stockStatus === "OUT_OF_STOCK") {
        filteredProducts = filteredProducts.filter(product => product.stock === 0);
      }
      
      // Rating filter
      if (filters.rating > 0) {
        filteredProducts = filteredProducts.filter(product => (product.rating || 0) >= filters.rating);
      }
      
      // Sorting
      filteredProducts.sort((a, b) => {
        let aValue: any = a[filters.sortBy as keyof Product];
        let bValue: any = b[filters.sortBy as keyof Product];
        
        if (filters.sortBy === "category") {
          aValue = a.category.name;
          bValue = b.category.name;
        }
        
        if (typeof aValue === "string") {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }
        
        if (filters.sortOrder === "asc") {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      setProducts(filteredProducts);
      setTotalItems(filteredProducts.length);
      setTotalPages(Math.ceil(filteredProducts.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    } finally {
      setLoading(false);
    }
  };

  const handleAISearch = async (query: string) => {
    try {
      setAiSearchLoading(true);
      setSearchQuery(query);
      
      // Simulate AI search - replace with actual AI API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success(`AI search completed for: "${query}"`);
    } catch (error) {
      console.error("Error in AI search:", error);
      toast.error("AI search failed");
    } finally {
      setAiSearchLoading(false);
    }
  };

  const handleFilterChange = (newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      category: "ALL",
      warehouse: "ALL",
      priceRange: { min: 0, max: 1000000 },
      stockStatus: "ALL",
      rating: 0,
      sortBy: "name",
      sortOrder: "asc",
    });
    setSearchQuery("");
    setCurrentPage(1);
  };

  const sortOptions = [
    { value: "name", label: "Name" },
    { value: "price", label: "Price" },
    { value: "stock", label: "Stock" },
    { value: "rating", label: "Rating" },
    { value: "category", label: "Category" },
    { value: "createdAt", label: "Date Added" },
  ];

  const sortOrderOptions = [
    { value: "asc", label: "Ascending" },
    { value: "desc", label: "Descending" },
  ];

  // Paginate products for current page
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedProducts = products.slice(startIndex, startIndex + itemsPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Product Catalog</h1>
          <p className="text-secondary-600">
            Browse and search through our product inventory
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
          >
            {viewMode === "grid" ? "List View" : "Grid View"}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </Button>
        </div>
      </div>

      {/* Search and Controls */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search products with AI..."
                value={searchQuery}
                onSearch={handleAISearch}
                loading={aiSearchLoading}
                className="max-w-md"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Select
                options={sortOptions}
                value={filters.sortBy}
                onChange={(value) => handleFilterChange({ sortBy: value })}
                className="w-32"
              />
              <Select
                options={sortOrderOptions}
                value={filters.sortOrder}
                onChange={(value) => handleFilterChange({ sortOrder: value as "asc" | "desc" })}
                className="w-32"
              />
            </div>
            <Button variant="outline" onClick={clearFilters}>
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-6">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="w-80">
            <ProductFilters
              filters={filters}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
            />
          </div>
        )}

        {/* Products Content */}
        <div className="flex-1">
          {/* Results Info */}
          <div className="flex items-center justify-between mb-4">
            <p className="text-secondary-600">
              Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, totalItems)} of {totalItems} products
            </p>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-secondary-600">Items per page:</span>
              <Select
                options={[
                  { value: "12", label: "12" },
                  { value: "24", label: "24" },
                  { value: "48", label: "48" },
                ]}
                value={itemsPerPage.toString()}
                onChange={(value) => setItemsPerPage(Number(value))}
                className="w-20"
              />
            </div>
          </div>

          {/* Products Display */}
          {loading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" text="Loading products..." />
            </div>
          ) : paginatedProducts.length === 0 ? (
            <EmptyState
              title="No products found"
              description="Try adjusting your search criteria or filters"
              action={{
                label: "Clear Filters",
                onClick: clearFilters,
              }}
            />
          ) : viewMode === "grid" ? (
            <ProductGridView products={paginatedProducts} canUpdate={canUpdate} />
          ) : (
            <ProductListView products={paginatedProducts} canUpdate={canUpdate} />
          )}

          {/* Pagination */}
          {!loading && paginatedProducts.length > 0 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={setCurrentPage}
                showInfo={false}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CatalogPage;
