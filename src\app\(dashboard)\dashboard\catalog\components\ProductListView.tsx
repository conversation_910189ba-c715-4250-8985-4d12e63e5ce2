"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Card,
  CardContent,
  Badge,
  Button,
  Modal,
} from "@/components/ui";
import { formatCurrency } from "@/lib/utils";
import { useToastActions } from "@/components/ui";
import ProductDetailsModal from "./ProductDetailsModal";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
  images?: string[];
  tags?: string[];
  rating?: number;
  reviewCount?: number;
  createdAt: string;
}

interface ProductListViewProps {
  products: Product[];
  canUpdate: boolean;
}

const ProductListView: React.FC<ProductListViewProps> = ({
  products,
  canUpdate,
}) => {
  const toast = useToastActions();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const handleAddToCart = (product: Product) => {
    // Simulate adding to cart - replace with actual cart logic
    toast.success(`${product.name} added to cart`);
  };

  const handleViewDetails = (product: Product) => {
    setSelectedProduct(product);
    setShowDetails(true);
  };

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="error" size="sm">Out of Stock</Badge>;
    } else if (stock <= 10) {
      return <Badge variant="warning" size="sm">Low Stock</Badge>;
    } else {
      return <Badge variant="success" size="sm">In Stock</Badge>;
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="w-4 h-4 text-yellow-400" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="half">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="transparent" />
            </linearGradient>
          </defs>
          <path
            fill="url(#half)"
            d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"
          />
        </svg>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-4 h-4 text-secondary-300" viewBox="0 0 20 20">
          <path
            fill="currentColor"
            d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"
          />
        </svg>
      );
    }

    return stars;
  };

  return (
    <>
      <div className="space-y-4">
        {products.map((product) => (
          <Card key={product.id} className="hover:shadow-md transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                {/* Product Image */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 relative overflow-hidden rounded-lg bg-secondary-50">
                    <Image
                      src={product.images?.[0] || "/api/placeholder/300/300"}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>

                {/* Product Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Category and Stock Status */}
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge variant="secondary" size="sm">
                          {product.category.name}
                        </Badge>
                        {getStockBadge(product.stock)}
                      </div>

                      {/* Product Name */}
                      <h3 className="text-lg font-semibold text-secondary-900 mb-1">
                        {product.name}
                      </h3>

                      {/* Description */}
                      {product.description && (
                        <p className="text-secondary-600 text-sm mb-2 line-clamp-2">
                          {product.description}
                        </p>
                      )}

                      {/* Rating */}
                      {product.rating && (
                        <div className="flex items-center space-x-1 mb-2">
                          <div className="flex items-center">
                            {renderStars(product.rating)}
                          </div>
                          <span className="text-sm text-secondary-600">
                            {product.rating} ({product.reviewCount || 0} reviews)
                          </span>
                        </div>
                      )}

                      {/* Tags */}
                      {product.tags && product.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {product.tags.slice(0, 4).map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                          {product.tags.length > 4 && (
                            <span className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded-full">
                              +{product.tags.length - 4}
                            </span>
                          )}
                        </div>
                      )}

                      {/* Warehouse */}
                      <div className="text-sm text-secondary-600">
                        Warehouse: {product.warehouse.name}
                      </div>
                    </div>

                    {/* Price and Actions */}
                    <div className="flex flex-col items-end space-y-3 ml-4">
                      {/* Price */}
                      <div className="text-right">
                        <div className="text-xl font-bold text-primary-600">
                          {formatCurrency(product.price)}
                        </div>
                        <div className="text-sm text-secondary-600">
                          Stock: {product.stock}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(product)}
                        >
                          View Details
                        </Button>
                        <Button
                          variant="primary"
                          size="sm"
                          disabled={product.stock === 0}
                          onClick={() => handleAddToCart(product)}
                        >
                          {product.stock === 0 ? "Out of Stock" : "Add to Cart"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Product Details Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => {
          setShowDetails(false);
          setSelectedProduct(null);
        }}
        title="Product Details"
        size="lg"
      >
        {selectedProduct && (
          <ProductDetailsModal
            product={selectedProduct}
            onClose={() => {
              setShowDetails(false);
              setSelectedProduct(null);
            }}
            onAddToCart={handleAddToCart}
            canUpdate={canUpdate}
          />
        )}
      </Modal>
    </>
  );
};

export default ProductListView;
