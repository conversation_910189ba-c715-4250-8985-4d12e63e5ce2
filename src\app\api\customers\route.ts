import { NextRequest } from "next/server";
import prisma from "@/db/client";
import { customerSchema } from "@/lib/validations";
import { PERMISSIONS } from "@/config/constants";
import {
  withAuth,
  successResponse,
  paginatedResponse,
  validateRequest,
  getQueryParams,
  buildSearchQuery,
  buildSortQuery,
  logApiRequest,
} from "@/lib/api-utils";

// GET /api/customers - Get all customers with pagination and search
export const GET = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "GET_CUSTOMERS");

    const { page, limit, query, sortBy, sortOrder, filters } = getQueryParams(req);

    // Build search conditions
    const searchConditions = buildSearchQuery(query, ["name", "email", "phone"]);
    
    // Build filter conditions
    const filterConditions: any = {};
    if (filters.hasOrders === "true") {
      filterConditions.orders = { some: {} };
    }
    if (filters.noOrders === "true") {
      filterConditions.orders = { none: {} };
    }

    const whereConditions = {
      ...searchConditions,
      ...filterConditions,
    };

    // Get total count
    const total = await prisma.customer.count({
      where: whereConditions,
    });

    // Get customers
    const customers = await prisma.customer.findMany({
      where: whereConditions,
      include: {
        orders: {
          select: {
            id: true,
            total: true,
            status: true,
            createdAt: true,
          },
          orderBy: { createdAt: "desc" },
          take: 5,
        },
        _count: {
          select: {
            orders: true,
          },
        },
      },
      orderBy: buildSortQuery(sortBy, sortOrder),
      skip: (page - 1) * limit,
      take: limit,
    });

    // Add customer statistics
    const customersWithStats = customers.map(customer => {
      const totalSpent = customer.orders.reduce((sum, order) => sum + order.total, 0);
      const averageOrderValue = customer.orders.length > 0 ? totalSpent / customer.orders.length : 0;
      const lastOrderDate = customer.orders.length > 0 ? customer.orders[0].createdAt : null;

      return {
        ...customer,
        stats: {
          totalOrders: customer._count.orders,
          totalSpent,
          averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
          lastOrderDate,
        },
      };
    });

    return paginatedResponse(customersWithStats, total, page, limit);
  },
  [PERMISSIONS.CUSTOMER_READ]
);

// POST /api/customers - Create new customer
export const POST = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "CREATE_CUSTOMER");

    const validatedData = await validateRequest(req, customerSchema);

    // Check if customer with email already exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: validatedData.email },
    });

    if (existingCustomer) {
      throw new Error("Customer with this email already exists");
    }

    // Create customer
    const customer = await prisma.customer.create({
      data: validatedData,
    });

    // Create notification
    await prisma.notification.create({
      data: {
        title: "New Customer Added",
        message: `Customer "${customer.name}" has been added to the system`,
        type: "INFO",
        priority: "LOW",
        metadata: {
          customerId: customer.id,
          customerName: customer.name,
          customerEmail: customer.email,
          addedBy: user.name,
        },
      },
    });

    return successResponse(customer, "Customer created successfully");
  },
  [PERMISSIONS.CUSTOMER_CREATE]
);

// PUT /api/customers - Bulk update customers
export const PUT = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "BULK_UPDATE_CUSTOMERS");

    const { customerIds, updates } = await req.json();

    if (!Array.isArray(customerIds) || customerIds.length === 0) {
      throw new Error("Customer IDs array is required");
    }

    // Validate updates
    const allowedUpdates = ["name", "phone", "address"];
    const validUpdates: any = {};
    
    for (const [key, value] of Object.entries(updates)) {
      if (allowedUpdates.includes(key)) {
        validUpdates[key] = value;
      }
    }

    if (Object.keys(validUpdates).length === 0) {
      throw new Error("No valid updates provided");
    }

    // Update customers
    const updatedCustomers = await prisma.$transaction(
      customerIds.map((id: string) =>
        prisma.customer.update({
          where: { id },
          data: validUpdates,
        })
      )
    );

    // Create notification
    await prisma.notification.create({
      data: {
        title: "Bulk Customer Update",
        message: `${customerIds.length} customers have been updated`,
        type: "INFO",
        priority: "LOW",
        metadata: {
          customerCount: customerIds.length,
          updates: validUpdates,
          updatedBy: user.name,
        },
      },
    });

    return successResponse(updatedCustomers, "Customers updated successfully");
  },
  [PERMISSIONS.CUSTOMER_UPDATE]
);

// DELETE /api/customers - Bulk delete customers
export const DELETE = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "BULK_DELETE_CUSTOMERS");

    const { customerIds } = await req.json();

    if (!Array.isArray(customerIds) || customerIds.length === 0) {
      throw new Error("Customer IDs array is required");
    }

    // Check if customers have orders
    const customersWithOrders = await prisma.customer.findMany({
      where: {
        id: { in: customerIds },
        orders: {
          some: {},
        },
      },
      select: { id: true, name: true },
    });

    if (customersWithOrders.length > 0) {
      throw new Error(
        `Cannot delete customers with existing orders: ${customersWithOrders
          .map((c) => c.name)
          .join(", ")}`
      );
    }

    // Delete customers
    const deletedCount = await prisma.customer.deleteMany({
      where: { id: { in: customerIds } },
    });

    // Create notification
    await prisma.notification.create({
      data: {
        title: "Customers Deleted",
        message: `${deletedCount.count} customers have been deleted`,
        type: "WARNING",
        priority: "MEDIUM",
        metadata: {
          deletedCount: deletedCount.count,
          deletedBy: user.name,
        },
      },
    });

    return successResponse(
      { deletedCount: deletedCount.count },
      "Customers deleted successfully"
    );
  },
  [PERMISSIONS.CUSTOMER_DELETE]
);
