"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>eader,
  CardTitle,
  CardContent,
  Table,
  <PERSON>ton,
  Badge,
  Modal,
  LoadingSpinner,
  EmptyState,
  Pagination,
  Select,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import OrderDetails from "./components/OrderDetails";
import CreateOrderModal from "./components/CreateOrderModal";

interface Order {
  id: string;
  customerName: string;
  customerEmail: string;
  status: "PENDING" | "PROCESSING" | "SHIPPED" | "DELIVERED" | "CANCELLED";
  total: number;
  discount?: number;
  items: Array<{
    id: string;
    productName: string;
    quantity: number;
    price: number;
  }>;
  createdAt: string;
  updatedAt: string;
}

const OrdersPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Modal states
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showCreateOrder, setShowCreateOrder] = useState(false);

  const canCreate = checkPermission(PERMISSIONS.ORDER_CREATE);
  const canUpdate = checkPermission(PERMISSIONS.ORDER_UPDATE);
  const canDelete = checkPermission(PERMISSIONS.ORDER_DELETE);

  useEffect(() => {
    fetchOrders();
  }, [currentPage, itemsPerPage, searchQuery, statusFilter]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API response
      const mockOrders: Order[] = [
        {
          id: "ORD-001",
          customerName: "Toko Berkah Jaya",
          customerEmail: "<EMAIL>",
          status: "PROCESSING",
          total: 2500000,
          discount: 125000,
          items: [
            { id: "1", productName: "Indomie Goreng", quantity: 100, price: 3000 },
            { id: "2", productName: "Teh Botol Sosro", quantity: 50, price: 4500 },
          ],
          createdAt: "2024-01-15T10:30:00Z",
          updatedAt: "2024-01-15T11:00:00Z",
        },
        {
          id: "ORD-002",
          customerName: "Warung Maju Mundur",
          customerEmail: "<EMAIL>",
          status: "SHIPPED",
          total: 1750000,
          items: [
            { id: "3", productName: "Chitato Sapi Panggang", quantity: 75, price: 8500 },
            { id: "4", productName: "Royco Kaldu Ayam", quantity: 200, price: 2500 },
          ],
          createdAt: "2024-01-15T09:15:00Z",
          updatedAt: "2024-01-15T14:30:00Z",
        },
        {
          id: "ORD-003",
          customerName: "Toko Sumber Rejeki",
          customerEmail: "<EMAIL>",
          status: "DELIVERED",
          total: 3200000,
          items: [
            { id: "5", productName: "Beras Premium", quantity: 20, price: 15000 },
            { id: "6", productName: "Minyak Goreng", quantity: 40, price: 18000 },
          ],
          createdAt: "2024-01-14T16:45:00Z",
          updatedAt: "2024-01-15T08:20:00Z",
        },
      ];

      // Apply filters
      let filteredOrders = mockOrders;
      
      if (searchQuery) {
        filteredOrders = filteredOrders.filter(order =>
          order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.customerName.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      
      if (statusFilter !== "ALL") {
        filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
      }

      setOrders(filteredOrders);
      setTotalItems(filteredOrders.length);
      setTotalPages(Math.ceil(filteredOrders.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to fetch orders");
    } finally {
      setLoading(false);
    }
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleUpdateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setOrders(prev => 
        prev.map(order => 
          order.id === orderId 
            ? { ...order, status: newStatus as any, updatedAt: new Date().toISOString() }
            : order
        )
      );
      
      toast.success("Order status updated successfully");
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");
    }
  };

  const handleCreateOrder = async (orderData: any) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newOrder: Order = {
        id: `ORD-${String(Date.now()).slice(-3)}`,
        ...orderData,
        status: "PENDING",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      setOrders(prev => [newOrder, ...prev]);
      setShowCreateOrder(false);
      toast.success("Order created successfully");
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("Failed to create order");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "warning" as const, label: "Pending" },
      PROCESSING: { variant: "info" as const, label: "Processing" },
      SHIPPED: { variant: "primary" as const, label: "Shipped" },
      DELIVERED: { variant: "success" as const, label: "Delivered" },
      CANCELLED: { variant: "error" as const, label: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "default" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const statusOptions = [
    { value: "ALL", label: "All Status" },
    { value: "PENDING", label: "Pending" },
    { value: "PROCESSING", label: "Processing" },
    { value: "SHIPPED", label: "Shipped" },
    { value: "DELIVERED", label: "Delivered" },
    { value: "CANCELLED", label: "Cancelled" },
  ];

  const columns = [
    {
      key: "id",
      title: "Order ID",
      dataIndex: "id",
      sortable: true,
    },
    {
      key: "customerName",
      title: "Customer",
      dataIndex: "customerName",
      sortable: true,
    },
    {
      key: "total",
      title: "Total",
      dataIndex: "total",
      render: (value: number) => formatCurrency(value),
      sortable: true,
    },
    {
      key: "status",
      title: "Status",
      dataIndex: "status",
      render: (value: string) => getStatusBadge(value),
    },
    {
      key: "createdAt",
      title: "Order Date",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
      sortable: true,
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, order: Order) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewOrder(order)}
          >
            View
          </Button>
          {canUpdate && order.status !== "DELIVERED" && order.status !== "CANCELLED" && (
            <Select
              options={[
                { value: "PENDING", label: "Pending" },
                { value: "PROCESSING", label: "Processing" },
                { value: "SHIPPED", label: "Shipped" },
                { value: "DELIVERED", label: "Delivered" },
                { value: "CANCELLED", label: "Cancel" },
              ]}
              value={order.status}
              onChange={(value) => handleUpdateOrderStatus(order.id, value)}
              className="w-32"
            />
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Orders</h1>
          <p className="text-secondary-600">Manage customer orders and transactions</p>
        </div>
        {canCreate && (
          <Button onClick={() => setShowCreateOrder(true)}>
            Create Order
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search orders..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Select
              options={statusOptions}
              value={statusFilter}
              onChange={setStatusFilter}
              className="w-40"
            />
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Orders ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading orders..." />
            </div>
          ) : orders.length === 0 ? (
            <EmptyState
              title="No orders found"
              description="No orders match your current filters"
              action={canCreate ? {
                label: "Create Order",
                onClick: () => setShowCreateOrder(true),
              } : undefined}
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={orders}
                loading={loading}
                onRowClick={handleViewOrder}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Order Details Modal */}
      <Modal
        isOpen={showOrderDetails}
        onClose={() => {
          setShowOrderDetails(false);
          setSelectedOrder(null);
        }}
        title="Order Details"
        size="lg"
      >
        {selectedOrder && (
          <OrderDetails
            order={selectedOrder}
            onStatusUpdate={handleUpdateOrderStatus}
            canUpdate={canUpdate}
          />
        )}
      </Modal>

      {/* Create Order Modal */}
      <Modal
        isOpen={showCreateOrder}
        onClose={() => setShowCreateOrder(false)}
        title="Create New Order"
        size="xl"
      >
        <CreateOrderModal
          onSave={handleCreateOrder}
          onCancel={() => setShowCreateOrder(false)}
        />
      </Modal>
    </div>
  );
};

export default OrdersPage;
