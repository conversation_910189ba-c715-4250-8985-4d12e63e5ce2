"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardContent,
  Table,
  Badge,
  Button,
  Select,
  LoadingSpinner,
  EmptyState,
  Pagination,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { formatDate } from "@/lib/utils";

interface InventoryMovement {
  id: string;
  productId: string;
  productName: string;
  type: "IN" | "OUT" | "TRANSFER" | "ADJUSTMENT";
  quantity: number;
  reason: string;
  fromWarehouse?: string;
  toWarehouse?: string;
  reference?: string;
  userName: string;
  createdAt: string;
}

interface InventoryMovementsProps {
  period: string;
  warehouse: string;
}

const InventoryMovements: React.FC<InventoryMovementsProps> = ({
  period,
  warehouse,
}) => {
  const [movements, setMovements] = useState<InventoryMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  useEffect(() => {
    fetchMovements();
  }, [period, warehouse, currentPage, itemsPerPage, searchQuery, typeFilter]);

  const fetchMovements = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock movements data
      const mockMovements: InventoryMovement[] = [
        {
          id: "MOV-001",
          productId: "P001",
          productName: "Indomie Goreng Original",
          type: "IN",
          quantity: 200,
          reason: "Purchase Order PO-2024-001",
          toWarehouse: "Gudang Utama",
          reference: "PO-2024-001",
          userName: "John Doe",
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "MOV-002",
          productId: "P002",
          productName: "Teh Botol Sosro 450ml",
          type: "OUT",
          quantity: -50,
          reason: "Sales Order SO-2024-001",
          fromWarehouse: "Gudang Utama",
          reference: "SO-2024-001",
          userName: "Jane Smith",
          createdAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "MOV-003",
          productId: "P003",
          productName: "Chitato Sapi Panggang",
          type: "TRANSFER",
          quantity: 25,
          reason: "Stock rebalancing",
          fromWarehouse: "Gudang Utama",
          toWarehouse: "Gudang Cabang",
          reference: "TRF-2024-001",
          userName: "Admin",
          createdAt: "2024-01-14T16:45:00Z",
        },
        {
          id: "MOV-004",
          productId: "P004",
          productName: "Royco Kaldu Ayam",
          type: "ADJUSTMENT",
          quantity: -5,
          reason: "Damaged goods write-off",
          fromWarehouse: "Gudang Utama",
          reference: "ADJ-2024-001",
          userName: "Warehouse Manager",
          createdAt: "2024-01-14T14:20:00Z",
        },
        {
          id: "MOV-005",
          productId: "P005",
          productName: "Beras Premium 5kg",
          type: "IN",
          quantity: 100,
          reason: "Emergency restock",
          toWarehouse: "Gudang Utama",
          reference: "PO-2024-002",
          userName: "Supply Manager",
          createdAt: "2024-01-13T11:30:00Z",
        },
      ];

      // Apply filters
      let filteredMovements = mockMovements;
      
      if (searchQuery) {
        filteredMovements = filteredMovements.filter(movement =>
          movement.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          movement.reason.toLowerCase().includes(searchQuery.toLowerCase()) ||
          movement.reference?.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      
      if (typeFilter !== "ALL") {
        filteredMovements = filteredMovements.filter(movement => movement.type === typeFilter);
      }

      setMovements(filteredMovements);
      setTotalItems(filteredMovements.length);
      setTotalPages(Math.ceil(filteredMovements.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching movements:", error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeBadge = (type: string, quantity: number) => {
    const typeConfig = {
      IN: { variant: "success" as const, label: "Stock In", icon: "↗️" },
      OUT: { variant: "error" as const, label: "Stock Out", icon: "↘️" },
      TRANSFER: { variant: "info" as const, label: "Transfer", icon: "↔️" },
      ADJUSTMENT: { variant: "warning" as const, label: "Adjustment", icon: "⚖️" },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || {
      variant: "default" as const,
      label: type,
      icon: "📦",
    };

    return (
      <div className="flex items-center space-x-2">
        <Badge variant={config.variant}>
          {config.icon} {config.label}
        </Badge>
      </div>
    );
  };

  const typeOptions = [
    { value: "ALL", label: "All Types" },
    { value: "IN", label: "Stock In" },
    { value: "OUT", label: "Stock Out" },
    { value: "TRANSFER", label: "Transfer" },
    { value: "ADJUSTMENT", label: "Adjustment" },
  ];

  const columns = [
    {
      key: "productName",
      title: "Product",
      dataIndex: "productName",
      sortable: true,
    },
    {
      key: "type",
      title: "Type",
      render: (_: any, movement: InventoryMovement) => 
        getTypeBadge(movement.type, movement.quantity),
    },
    {
      key: "quantity",
      title: "Quantity",
      dataIndex: "quantity",
      render: (value: number) => (
        <span className={`font-medium ${value > 0 ? "text-success-600" : "text-error-600"}`}>
          {value > 0 ? "+" : ""}{value.toLocaleString()}
        </span>
      ),
      sortable: true,
    },
    {
      key: "warehouse",
      title: "Warehouse",
      render: (_: any, movement: InventoryMovement) => {
        if (movement.type === "TRANSFER") {
          return (
            <div className="text-sm">
              <div>{movement.fromWarehouse} → {movement.toWarehouse}</div>
            </div>
          );
        }
        return movement.fromWarehouse || movement.toWarehouse || "-";
      },
    },
    {
      key: "reason",
      title: "Reason",
      dataIndex: "reason",
    },
    {
      key: "reference",
      title: "Reference",
      dataIndex: "reference",
      render: (value: string) => value || "-",
    },
    {
      key: "userName",
      title: "User",
      dataIndex: "userName",
    },
    {
      key: "createdAt",
      title: "Date",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
      sortable: true,
    },
  ];

  // Calculate movement summary
  const movementSummary = movements.reduce((acc, movement) => {
    acc[movement.type] = (acc[movement.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Movement Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-success-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Stock In</p>
                <p className="text-2xl font-bold text-success-600">
                  {movementSummary.IN || 0}
                </p>
              </div>
              <div className="text-2xl">↗️</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-error-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Stock Out</p>
                <p className="text-2xl font-bold text-error-600">
                  {movementSummary.OUT || 0}
                </p>
              </div>
              <div className="text-2xl">↘️</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-info-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Transfers</p>
                <p className="text-2xl font-bold text-info-600">
                  {movementSummary.TRANSFER || 0}
                </p>
              </div>
              <div className="text-2xl">↔️</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-warning-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Adjustments</p>
                <p className="text-2xl font-bold text-warning-600">
                  {movementSummary.ADJUSTMENT || 0}
                </p>
              </div>
              <div className="text-2xl">⚖️</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search movements..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Select
              options={typeOptions}
              value={typeFilter}
              onChange={setTypeFilter}
              className="w-40"
            />
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Movements Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Movements ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading movements..." />
            </div>
          ) : movements.length === 0 ? (
            <EmptyState
              title="No movements found"
              description="No inventory movements match your current filters"
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={movements}
                loading={loading}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default InventoryMovements;
