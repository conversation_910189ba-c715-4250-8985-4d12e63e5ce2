"use client";

import React, { useState, useEffect } from "react";
import { Button, Input, Select } from "@/components/ui";
import { FormField } from "@/components/forms";
import { useToastActions } from "@/components/ui";

interface Category {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  isActive: boolean;
}

interface CategoryFormProps {
  category?: Category | null;
  onSave: (categoryData: any) => void;
  onCancel: () => void;
}

interface FormData {
  name: string;
  description: string;
  parentId: string;
  isActive: boolean;
}

interface FormErrors {
  name?: string;
  description?: string;
}

const CategoryForm: React.FC<CategoryFormProps> = ({ category, onSave, onCancel }) => {
  const toast = useToastActions();
  const [loading, setLoading] = useState(false);
  const [parentCategories, setParentCategories] = useState<Array<{ value: string; label: string }>>([]);
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    parentId: "",
    isActive: true,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    fetchParentCategories();

    // Populate form if editing
    if (category) {
      setFormData({
        name: category.name,
        description: category.description || "",
        parentId: category.parentId || "",
        isActive: category.isActive,
      });
    }
  }, [category]);

  const fetchParentCategories = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data - exclude current category if editing
      const mockCategories = [
        { value: "", label: "No Parent Category" },
        { value: "1", label: "Makanan Instan" },
        { value: "2", label: "Minuman" },
        { value: "3", label: "Makanan Ringan" },
        { value: "4", label: "Bumbu Dapur" },
        { value: "5", label: "Bahan Pokok" },
      ].filter(cat => !category || cat.value !== category.id);

      setParentCategories(mockCategories);
    } catch (error) {
      console.error("Error fetching parent categories:", error);
      toast.error("Failed to load parent categories");
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Category name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Category name must be at least 2 characters";
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "Description must be less than 500 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const categoryData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        parentId: formData.parentId || undefined,
        isActive: formData.isActive,
      };

      await onSave(categoryData);
    } catch (error) {
      console.error("Error saving category:", error);
      toast.error("Failed to save category");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const statusOptions = [
    { value: "true", label: "Active" },
    { value: "false", label: "Inactive" },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <FormField
        label="Category Name"
        required
        error={errors.name}
      >
        <Input
          placeholder="Enter category name"
          value={formData.name}
          onChange={(e) => handleInputChange("name", e.target.value)}
        />
      </FormField>

      <FormField
        label="Parent Category"
        helperText="Select a parent category to create a subcategory"
      >
        <Select
          options={parentCategories}
          value={formData.parentId}
          onChange={(value) => handleInputChange("parentId", value)}
          placeholder="Select parent category"
        />
      </FormField>

      <FormField
        label="Description"
        error={errors.description}
        helperText="Optional description for the category"
      >
        <textarea
          className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
          rows={3}
          placeholder="Enter category description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          maxLength={500}
        />
        <div className="text-xs text-secondary-500 mt-1">
          {formData.description.length}/500 characters
        </div>
      </FormField>

      <FormField
        label="Status"
        helperText="Active categories are visible in product forms"
      >
        <Select
          options={statusOptions}
          value={formData.isActive.toString()}
          onChange={(value) => handleInputChange("isActive", value === "true")}
        />
      </FormField>

      {/* Preview */}
      <div className="p-4 bg-secondary-50 rounded-lg">
        <h4 className="font-medium text-secondary-900 mb-2">Preview</h4>
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <span className="font-medium">{formData.name || "Category Name"}</span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              formData.isActive 
                ? "bg-success-100 text-success-800" 
                : "bg-secondary-100 text-secondary-800"
            }`}>
              {formData.isActive ? "Active" : "Inactive"}
            </span>
          </div>
          {formData.parentId && (
            <div className="text-sm text-secondary-600">
              Parent: {parentCategories.find(p => p.value === formData.parentId)?.label}
            </div>
          )}
          {formData.description && (
            <p className="text-sm text-secondary-600">{formData.description}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-secondary-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
        >
          {category ? "Update Category" : "Create Category"}
        </Button>
      </div>
    </form>
  );
};

export default CategoryForm;
