import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps {
  options: SelectOption[];
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  label?: string;
  error?: string;
  helperText?: string;
  disabled?: boolean;
  clearable?: boolean;
  searchable?: boolean;
  className?: string;
  onChange?: (value: string) => void;
  onClear?: () => void;
}

const Select: React.FC<SelectProps> = ({
  options,
  value,
  defaultValue,
  placeholder = "Select an option",
  label,
  error,
  helperText,
  disabled = false,
  clearable = false,
  searchable = false,
  className,
  onChange,
  onClear,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value || defaultValue || "");
  const [searchTerm, setSearchTerm] = useState("");
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const selectedOption = options.find(option => option.value === selectedValue);
  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (option: SelectOption) => {
    if (option.disabled) return;
    
    setSelectedValue(option.value);
    setIsOpen(false);
    setSearchTerm("");
    onChange?.(option.value);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedValue("");
    onClear?.();
  };

  const handleToggle = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
    if (searchable && !isOpen) {
      setTimeout(() => inputRef.current?.focus(), 0);
    }
  };

  const selectId = `select-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <label 
          htmlFor={selectId}
          className="block text-sm font-medium text-secondary-700 mb-1"
        >
          {label}
        </label>
      )}
      
      <div className="relative" ref={selectRef}>
        <div
          className={cn(
            "relative w-full cursor-pointer rounded-lg border bg-white py-2 pl-3 pr-10 text-left transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1",
            error
              ? "border-error-500 focus:border-error-500 focus:ring-error-500"
              : "border-secondary-300 focus:border-primary-500 focus:ring-primary-500",
            disabled && "opacity-50 cursor-not-allowed bg-secondary-50",
            isOpen && "ring-2 ring-primary-500 border-primary-500"
          )}
          onClick={handleToggle}
        >
          {searchable && isOpen ? (
            <input
              ref={inputRef}
              type="text"
              className="w-full border-none outline-none bg-transparent text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search..."
            />
          ) : (
            <span className={cn(
              "block truncate text-sm",
              selectedOption ? "text-secondary-900" : "text-secondary-500"
            )}>
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          )}
          
          <span className="absolute inset-y-0 right-0 flex items-center pr-2">
            {clearable && selectedValue && !disabled && (
              <button
                type="button"
                className="mr-1 text-secondary-400 hover:text-secondary-600"
                onClick={handleClear}
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            <svg
              className={cn(
                "h-5 w-5 text-secondary-400 transition-transform duration-200",
                isOpen && "rotate-180"
              )}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </div>

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full rounded-lg bg-white shadow-lg border border-secondary-200 max-h-60 overflow-auto">
            {filteredOptions.length === 0 ? (
              <div className="py-2 px-3 text-sm text-secondary-500">
                No options found
              </div>
            ) : (
              filteredOptions.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    "cursor-pointer select-none py-2 px-3 text-sm transition-colors",
                    option.disabled
                      ? "text-secondary-400 cursor-not-allowed"
                      : "text-secondary-900 hover:bg-primary-50 hover:text-primary-900",
                    selectedValue === option.value && "bg-primary-100 text-primary-900"
                  )}
                  onClick={() => handleSelect(option)}
                >
                  {option.label}
                </div>
              ))
            )}
          </div>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-error-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="mt-1 text-sm text-secondary-500">{helperText}</p>
      )}
    </div>
  );
};

export { Select };
