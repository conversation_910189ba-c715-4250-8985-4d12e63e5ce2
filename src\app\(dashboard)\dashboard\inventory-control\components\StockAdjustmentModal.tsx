"use client";

import React, { useState, useEffect } from "react";
import { Button, Input, Select } from "@/components/ui";
import { FormField, SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";

interface Product {
  id: string;
  name: string;
  currentStock: number;
  minStock?: number;
  category: string;
  warehouse: string;
}

interface StockAdjustmentModalProps {
  product?: Product | null;
  onSave: (adjustmentData: any) => void;
  onCancel: () => void;
}

interface FormData {
  productId: string;
  productName: string;
  adjustmentType: "INCREASE" | "DECREASE";
  quantity: string;
  reason: string;
  notes: string;
  warehouse: string;
}

interface FormErrors {
  productId?: string;
  quantity?: string;
  reason?: string;
}

const StockAdjustmentModal: React.FC<StockAdjustmentModalProps> = ({
  product,
  onSave,
  onCancel,
}) => {
  const toast = useToastActions();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [productSearch, setProductSearch] = useState("");
  
  const [formData, setFormData] = useState<FormData>({
    productId: "",
    productName: "",
    adjustmentType: "INCREASE",
    quantity: "",
    reason: "",
    notes: "",
    warehouse: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    fetchProducts();

    // Populate form if product is provided
    if (product) {
      setFormData({
        productId: product.id,
        productName: product.name,
        adjustmentType: product.currentStock < (product.minStock || 0) ? "INCREASE" : "DECREASE",
        quantity: "",
        reason: "",
        notes: "",
        warehouse: product.warehouse,
      });
    }
  }, [product]);

  const fetchProducts = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data
      setProducts([
        {
          id: "1",
          name: "Indomie Goreng Original",
          currentStock: 450,
          minStock: 50,
          category: "Makanan Instan",
          warehouse: "Gudang Utama",
        },
        {
          id: "2",
          name: "Teh Botol Sosro 450ml",
          currentStock: 5,
          minStock: 20,
          category: "Minuman",
          warehouse: "Gudang Utama",
        },
        {
          id: "3",
          name: "Chitato Sapi Panggang",
          currentStock: 0,
          minStock: 25,
          category: "Makanan Ringan",
          warehouse: "Gudang Cabang",
        },
        {
          id: "4",
          name: "Royco Kaldu Ayam",
          currentStock: 300,
          minStock: 30,
          category: "Bumbu Dapur",
          warehouse: "Gudang Utama",
        },
        {
          id: "5",
          name: "Beras Premium 5kg",
          currentStock: 85,
          minStock: 20,
          category: "Bahan Pokok",
          warehouse: "Gudang Utama",
        },
      ]);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.productId) {
      newErrors.productId = "Please select a product";
    }

    if (!formData.quantity.trim()) {
      newErrors.quantity = "Quantity is required";
    } else if (isNaN(Number(formData.quantity)) || Number(formData.quantity) <= 0) {
      newErrors.quantity = "Quantity must be a positive number";
    } else {
      const selectedProduct = products.find(p => p.id === formData.productId);
      if (selectedProduct && formData.adjustmentType === "DECREASE") {
        if (Number(formData.quantity) > selectedProduct.currentStock) {
          newErrors.quantity = `Cannot decrease by more than current stock (${selectedProduct.currentStock})`;
        }
      }
    }

    if (!formData.reason.trim()) {
      newErrors.reason = "Reason is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const selectedProduct = products.find(p => p.id === formData.productId);
      const adjustmentQuantity = formData.adjustmentType === "INCREASE" 
        ? Number(formData.quantity)
        : -Number(formData.quantity);

      const adjustmentData = {
        productId: formData.productId,
        productName: formData.productName || selectedProduct?.name,
        adjustmentType: formData.adjustmentType,
        quantity: adjustmentQuantity,
        reason: formData.reason.trim(),
        notes: formData.notes.trim(),
        warehouse: formData.warehouse || selectedProduct?.warehouse,
        currentStock: selectedProduct?.currentStock,
        newStock: (selectedProduct?.currentStock || 0) + adjustmentQuantity,
      };

      await onSave(adjustmentData);
    } catch (error) {
      console.error("Error recording adjustment:", error);
      toast.error("Failed to record adjustment");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleProductSelect = (productId: string) => {
    const selectedProduct = products.find(p => p.id === productId);
    if (selectedProduct) {
      setFormData(prev => ({
        ...prev,
        productId: selectedProduct.id,
        productName: selectedProduct.name,
        warehouse: selectedProduct.warehouse,
        adjustmentType: selectedProduct.currentStock < (selectedProduct.minStock || 0) ? "INCREASE" : "DECREASE",
      }));
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(productSearch.toLowerCase()) &&
    (!formData.productId || product.id === formData.productId)
  );

  const productOptions = products.map(product => ({
    value: product.id,
    label: `${product.name} (Stock: ${product.currentStock})`,
  }));

  const adjustmentTypeOptions = [
    { value: "INCREASE", label: "Increase Stock" },
    { value: "DECREASE", label: "Decrease Stock" },
  ];

  const reasonOptions = [
    { value: "Damaged goods", label: "Damaged goods" },
    { value: "Expired products", label: "Expired products" },
    { value: "Stock count correction", label: "Stock count correction" },
    { value: "Theft/Loss", label: "Theft/Loss" },
    { value: "Supplier return", label: "Supplier return" },
    { value: "Quality control", label: "Quality control" },
    { value: "Emergency restock", label: "Emergency restock" },
    { value: "Warehouse transfer", label: "Warehouse transfer" },
    { value: "Other", label: "Other (specify in notes)" },
  ];

  const selectedProduct = products.find(p => p.id === formData.productId);
  const adjustmentQuantity = Number(formData.quantity) || 0;
  const newStock = selectedProduct 
    ? selectedProduct.currentStock + (formData.adjustmentType === "INCREASE" ? adjustmentQuantity : -adjustmentQuantity)
    : 0;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Product Selection */}
      {!product && (
        <FormField
          label="Product"
          required
          error={errors.productId}
        >
          <div className="space-y-2">
            <SearchInput
              placeholder="Search products..."
              value={productSearch}
              onSearch={setProductSearch}
            />
            
            {productSearch && filteredProducts.length > 0 && (
              <div className="max-h-48 overflow-y-auto border border-secondary-200 rounded-lg">
                {filteredProducts.map(product => (
                  <button
                    key={product.id}
                    type="button"
                    onClick={() => {
                      handleProductSelect(product.id);
                      setProductSearch("");
                    }}
                    className="w-full p-3 text-left hover:bg-secondary-50 border-b border-secondary-100 last:border-b-0"
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-secondary-900">{product.name}</p>
                        <p className="text-sm text-secondary-600">{product.category}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">Stock: {product.currentStock}</p>
                        <p className="text-sm text-secondary-600">{product.warehouse}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
            
            {formData.productId && (
              <Select
                options={productOptions}
                value={formData.productId}
                onChange={handleProductSelect}
                placeholder="Select product"
              />
            )}
          </div>
        </FormField>
      )}

      {/* Product Info Display */}
      {selectedProduct && (
        <div className="p-4 bg-secondary-50 rounded-lg">
          <h4 className="font-medium text-secondary-900 mb-2">Selected Product</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-secondary-600">Product:</span>
              <div className="font-medium">{selectedProduct.name}</div>
            </div>
            <div>
              <span className="text-secondary-600">Category:</span>
              <div className="font-medium">{selectedProduct.category}</div>
            </div>
            <div>
              <span className="text-secondary-600">Current Stock:</span>
              <div className="font-medium">{selectedProduct.currentStock}</div>
            </div>
            <div>
              <span className="text-secondary-600">Warehouse:</span>
              <div className="font-medium">{selectedProduct.warehouse}</div>
            </div>
          </div>
        </div>
      )}

      {/* Adjustment Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Adjustment Type"
          required
        >
          <Select
            options={adjustmentTypeOptions}
            value={formData.adjustmentType}
            onChange={(value) => handleInputChange("adjustmentType", value)}
          />
        </FormField>

        <FormField
          label="Quantity"
          required
          error={errors.quantity}
        >
          <Input
            type="number"
            placeholder="0"
            value={formData.quantity}
            onChange={(e) => handleInputChange("quantity", e.target.value)}
            min="1"
          />
        </FormField>
      </div>

      {/* Stock Preview */}
      {selectedProduct && formData.quantity && (
        <div className="p-4 bg-secondary-50 rounded-lg">
          <h4 className="font-medium text-secondary-900 mb-2">Stock Preview</h4>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-secondary-600">Current Stock:</span>
              <div className="font-medium">{selectedProduct.currentStock}</div>
            </div>
            <div>
              <span className="text-secondary-600">Adjustment:</span>
              <div className={`font-medium ${formData.adjustmentType === "INCREASE" ? "text-success-600" : "text-error-600"}`}>
                {formData.adjustmentType === "INCREASE" ? "+" : "-"}{adjustmentQuantity}
              </div>
            </div>
            <div>
              <span className="text-secondary-600">New Stock:</span>
              <div className="font-medium">{newStock}</div>
            </div>
          </div>
        </div>
      )}

      <FormField
        label="Reason"
        required
        error={errors.reason}
      >
        <Select
          options={reasonOptions}
          value={formData.reason}
          onChange={(value) => handleInputChange("reason", value)}
          placeholder="Select reason"
        />
      </FormField>

      <FormField
        label="Additional Notes"
        helperText="Optional additional information about this adjustment"
      >
        <textarea
          className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
          rows={3}
          placeholder="Enter additional notes (optional)"
          value={formData.notes}
          onChange={(e) => handleInputChange("notes", e.target.value)}
        />
      </FormField>

      <div className="flex justify-end space-x-3 pt-4 border-t border-secondary-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
        >
          Record Adjustment
        </Button>
      </div>
    </form>
  );
};

export default StockAdjustmentModal;
