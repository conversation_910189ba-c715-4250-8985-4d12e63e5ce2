"use client";

import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Select,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Badge,
  LoadingSpinner,
} from "@/components/ui";
import { FormField, SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  category: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
}

interface CartItem {
  productId: string;
  productName: string;
  price: number;
  quantity: number;
  maxStock: number;
}

interface CreateOrderModalProps {
  onSave: (orderData: any) => void;
  onCancel: () => void;
}

const CreateOrderModal: React.FC<CreateOrderModalProps> = ({ onSave, onCancel }) => {
  const toast = useToastActions();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  
  const [selectedCustomerId, setSelectedCustomerId] = useState("");
  const [productSearch, setProductSearch] = useState("");
  const [discountPercentage, setDiscountPercentage] = useState(0);
  const [notes, setNotes] = useState("");

  useEffect(() => {
    fetchCustomers();
    fetchProducts();
  }, []);

  const fetchCustomers = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data
      setCustomers([
        { id: "1", name: "Toko Berkah Jaya", email: "<EMAIL>" },
        { id: "2", name: "Warung Maju Mundur", email: "<EMAIL>" },
        { id: "3", name: "Toko Sumber Rejeki", email: "<EMAIL>" },
      ]);
    } catch (error) {
      console.error("Error fetching customers:", error);
      toast.error("Failed to load customers");
    }
  };

  const fetchProducts = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data
      setProducts([
        { id: "1", name: "Indomie Goreng", price: 3000, stock: 500, category: "Makanan Instan" },
        { id: "2", name: "Teh Botol Sosro", price: 4500, stock: 200, category: "Minuman" },
        { id: "3", name: "Chitato Sapi Panggang", price: 8500, stock: 150, category: "Makanan Ringan" },
        { id: "4", name: "Royco Kaldu Ayam", price: 2500, stock: 300, category: "Bumbu Dapur" },
        { id: "5", name: "Beras Premium", price: 15000, stock: 100, category: "Bahan Pokok" },
      ]);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(productSearch.toLowerCase()) &&
    product.stock > 0 &&
    !cart.some(item => item.productId === product.id)
  );

  const addToCart = (product: Product) => {
    const cartItem: CartItem = {
      productId: product.id,
      productName: product.name,
      price: product.price,
      quantity: 1,
      maxStock: product.stock,
    };
    
    setCart(prev => [...prev, cartItem]);
    setProductSearch("");
  };

  const updateCartItemQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCart(prev =>
      prev.map(item =>
        item.productId === productId
          ? { ...item, quantity: Math.min(quantity, item.maxStock) }
          : item
      )
    );
  };

  const removeFromCart = (productId: string) => {
    setCart(prev => prev.filter(item => item.productId !== productId));
  };

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const calculateDiscount = () => {
    return (calculateSubtotal() * discountPercentage) / 100;
  };

  const calculateTotal = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCustomerId) {
      toast.error("Please select a customer");
      return;
    }

    if (cart.length === 0) {
      toast.error("Please add at least one product to the cart");
      return;
    }

    try {
      setLoading(true);

      const selectedCustomer = customers.find(c => c.id === selectedCustomerId);
      
      const orderData = {
        customerName: selectedCustomer?.name,
        customerEmail: selectedCustomer?.email,
        total: calculateTotal(),
        discount: calculateDiscount(),
        items: cart.map(item => ({
          id: item.productId,
          productName: item.productName,
          quantity: item.quantity,
          price: item.price,
        })),
        notes,
      };

      await onSave(orderData);
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("Failed to create order");
    } finally {
      setLoading(false);
    }
  };

  const customerOptions = customers.map(customer => ({
    value: customer.id,
    label: `${customer.name} (${customer.email})`,
  }));

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Customer & Products */}
        <div className="space-y-6">
          {/* Customer Selection */}
          <FormField label="Customer" required>
            <Select
              options={customerOptions}
              value={selectedCustomerId}
              onChange={setSelectedCustomerId}
              placeholder="Select customer"
              searchable
            />
          </FormField>

          {/* Product Search */}
          <FormField label="Add Products">
            <SearchInput
              placeholder="Search products..."
              value={productSearch}
              onSearch={setProductSearch}
            />
            
            {productSearch && (
              <div className="mt-2 max-h-48 overflow-y-auto border border-secondary-200 rounded-lg">
                {filteredProducts.length > 0 ? (
                  filteredProducts.map(product => (
                    <div
                      key={product.id}
                      className="p-3 hover:bg-secondary-50 cursor-pointer border-b border-secondary-100 last:border-b-0"
                      onClick={() => addToCart(product)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium text-secondary-900">{product.name}</p>
                          <p className="text-sm text-secondary-600">{product.category}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-primary-600">
                            {formatCurrency(product.price)}
                          </p>
                          <p className="text-sm text-secondary-600">
                            Stock: {product.stock}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-3 text-center text-secondary-500">
                    No products found
                  </div>
                )}
              </div>
            )}
          </FormField>

          {/* Discount */}
          <FormField label="Discount (%)">
            <Input
              type="number"
              placeholder="0"
              value={discountPercentage}
              onChange={(e) => setDiscountPercentage(Math.max(0, Math.min(100, Number(e.target.value))))}
              min="0"
              max="100"
            />
          </FormField>

          {/* Notes */}
          <FormField label="Notes">
            <textarea
              className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
              rows={3}
              placeholder="Order notes (optional)"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </FormField>
        </div>

        {/* Right Column - Cart */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Cart ({cart.length} items)</CardTitle>
            </CardHeader>
            <CardContent>
              {cart.length === 0 ? (
                <div className="text-center py-8 text-secondary-500">
                  No items in cart
                </div>
              ) : (
                <div className="space-y-4">
                  {cart.map(item => (
                    <div key={item.productId} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium text-secondary-900">{item.productName}</p>
                        <p className="text-sm text-secondary-600">
                          {formatCurrency(item.price)} each
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => updateCartItemQuantity(item.productId, item.quantity - 1)}
                        >
                          -
                        </Button>
                        <span className="w-12 text-center">{item.quantity}</span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => updateCartItemQuantity(item.productId, item.quantity + 1)}
                          disabled={item.quantity >= item.maxStock}
                        >
                          +
                        </Button>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() => removeFromCart(item.productId)}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  {/* Order Summary */}
                  <div className="border-t pt-4 space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(calculateSubtotal())}</span>
                    </div>
                    {discountPercentage > 0 && (
                      <div className="flex justify-between text-success-600">
                        <span>Discount ({discountPercentage}%):</span>
                        <span>-{formatCurrency(calculateDiscount())}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-lg font-semibold border-t pt-2">
                      <span>Total:</span>
                      <span>{formatCurrency(calculateTotal())}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-secondary-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
          disabled={!selectedCustomerId || cart.length === 0}
        >
          Create Order
        </Button>
      </div>
    </form>
  );
};

export default CreateOrderModal;
