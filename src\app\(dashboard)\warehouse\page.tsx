"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardTitle,
  CardContent,
  Stats,
  Button,
  Select,
  Badge,
  Modal,
  LoadingSpinner,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import WarehouseOverview from "./components/WarehouseOverview";
import StockMovements from "./components/StockMovements";
import ReceivingDock from "./components/ReceivingDock";
import ShippingDock from "./components/ShippingDock";
import StockTransferModal from "./components/StockTransferModal";

interface WarehouseStats {
  totalProducts: number;
  totalValue: number;
  pendingReceiving: number;
  pendingShipping: number;
  lowStockAlerts: number;
  utilizationRate: number;
  todayMovements: number;
  activeTransfers: number;
}

const WarehousePage: React.FC = () => {
  const { user, checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [stats, setStats] = useState<WarehouseStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedWarehouse, setSelectedWarehouse] = useState("1");
  
  // Modal states
  const [showTransferModal, setShowTransferModal] = useState(false);

  const canManageWarehouse = checkPermission(PERMISSIONS.INVENTORY_UPDATE);
  const canViewReports = checkPermission(PERMISSIONS.REPORTS_READ);

  useEffect(() => {
    fetchWarehouseData();
  }, [selectedWarehouse]);

  const fetchWarehouseData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock warehouse stats
      setStats({
        totalProducts: 89,
        totalValue: 156780000,
        pendingReceiving: 12,
        pendingShipping: 8,
        lowStockAlerts: 15,
        utilizationRate: 78.5,
        todayMovements: 45,
        activeTransfers: 3,
      });
    } catch (error) {
      console.error("Error fetching warehouse data:", error);
      toast.error("Failed to fetch warehouse data");
    } finally {
      setLoading(false);
    }
  };

  const handleStockTransfer = async (transferData: any) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Stock transfer initiated successfully");
      setShowTransferModal(false);
      
      // Refresh data
      fetchWarehouseData();
    } catch (error) {
      console.error("Error initiating stock transfer:", error);
      toast.error("Failed to initiate stock transfer");
    }
  };

  const warehouseOptions = [
    { value: "1", label: "Gudang Utama" },
    { value: "2", label: "Gudang Cabang" },
    { value: "3", label: "Gudang Frozen" },
  ];

  const tabs = [
    { id: "overview", label: "Overview", icon: "📊" },
    { id: "receiving", label: "Receiving", icon: "📥", count: stats?.pendingReceiving },
    { id: "shipping", label: "Shipping", icon: "📤", count: stats?.pendingShipping },
    { id: "movements", label: "Movements", icon: "🔄" },
  ];

  const statsData = stats ? [
    {
      label: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      change: {
        value: 3.2,
        type: "increase" as const,
        period: "vs yesterday",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Inventory Value",
      value: formatCurrency(stats.totalValue),
      change: {
        value: 8.7,
        type: "increase" as const,
        period: "vs last week",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: "success" as const,
    },
    {
      label: "Pending Tasks",
      value: (stats.pendingReceiving + stats.pendingShipping).toLocaleString(),
      change: {
        value: 12.5,
        type: "decrease" as const,
        period: "vs yesterday",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: "warning" as const,
    },
    {
      label: "Utilization Rate",
      value: `${stats.utilizationRate}%`,
      change: {
        value: 2.1,
        type: "increase" as const,
        period: "vs last month",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      color: "info" as const,
    },
  ] : [];

  if (!canManageWarehouse) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-secondary-900 mb-2">
            Access Denied
          </h2>
          <p className="text-secondary-600">
            You don't have permission to access warehouse management.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading warehouse data..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Warehouse Management</h1>
          <p className="text-secondary-600">
            Manager: {user?.name} | Current Warehouse: {warehouseOptions.find(w => w.value === selectedWarehouse)?.label}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Select
            options={warehouseOptions}
            value={selectedWarehouse}
            onChange={setSelectedWarehouse}
            className="w-48"
          />
          <Button onClick={() => setShowTransferModal(true)}>
            Stock Transfer
          </Button>
          <Button variant="outline">
            Generate Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <Stats stats={statsData} columns={4} />

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">📥</span>
            </div>
            <h3 className="font-medium text-secondary-900">Receive Stock</h3>
            <p className="text-sm text-secondary-600">Process incoming deliveries</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">📤</span>
            </div>
            <h3 className="font-medium text-secondary-900">Ship Orders</h3>
            <p className="text-sm text-secondary-600">Process outgoing shipments</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">📋</span>
            </div>
            <h3 className="font-medium text-secondary-900">Stock Count</h3>
            <p className="text-sm text-secondary-600">Perform inventory audit</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-info-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">🔄</span>
            </div>
            <h3 className="font-medium text-secondary-900">Transfer Stock</h3>
            <p className="text-sm text-secondary-600">Move between locations</p>
          </CardContent>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-secondary-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"
              }`}
            >
              <div className="flex items-center space-x-2">
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
                {tab.count && tab.count > 0 && (
                  <Badge variant="error" size="sm">
                    {tab.count}
                  </Badge>
                )}
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === "overview" && (
          <WarehouseOverview
            stats={stats}
            warehouseId={selectedWarehouse}
            onStockTransfer={() => setShowTransferModal(true)}
          />
        )}
        
        {activeTab === "receiving" && (
          <ReceivingDock
            warehouseId={selectedWarehouse}
            pendingCount={stats?.pendingReceiving || 0}
          />
        )}
        
        {activeTab === "shipping" && (
          <ShippingDock
            warehouseId={selectedWarehouse}
            pendingCount={stats?.pendingShipping || 0}
          />
        )}
        
        {activeTab === "movements" && (
          <StockMovements
            warehouseId={selectedWarehouse}
          />
        )}
      </div>

      {/* Stock Transfer Modal */}
      <Modal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        title="Stock Transfer"
        size="lg"
      >
        <StockTransferModal
          fromWarehouse={selectedWarehouse}
          onTransfer={handleStockTransfer}
          onCancel={() => setShowTransferModal(false)}
        />
      </Modal>
    </div>
  );
};

export default WarehousePage;
