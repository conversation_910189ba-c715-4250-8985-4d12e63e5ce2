"use client";

import React, { useState, useEffect } from "react";
import { Button, Input, Select } from "@/components/ui";
import { FormField } from "@/components/forms";
import { useToastActions } from "@/components/ui";

interface Product {
  id: string;
  name: string;
  currentStock: number;
}

interface InventoryAdjustmentModalProps {
  onSave: (adjustmentData: any) => void;
  onCancel: () => void;
}

interface FormData {
  productId: string;
  adjustmentType: "INCREASE" | "DECREASE";
  quantity: string;
  reason: string;
}

interface FormErrors {
  productId?: string;
  quantity?: string;
  reason?: string;
}

const InventoryAdjustmentModal: React.FC<InventoryAdjustmentModalProps> = ({
  onSave,
  onCancel,
}) => {
  const toast = useToastActions();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  
  const [formData, setFormData] = useState<FormData>({
    productId: "",
    adjustmentType: "INCREASE",
    quantity: "",
    reason: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data
      setProducts([
        { id: "1", name: "Indomie Goreng", currentStock: 500 },
        { id: "2", name: "Teh Botol Sosro", currentStock: 8 },
        { id: "3", name: "Chitato Sapi Panggang", currentStock: 150 },
        { id: "4", name: "Royco Kaldu Ayam", currentStock: 300 },
        { id: "5", name: "Beras Premium", currentStock: 0 },
      ]);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.productId) {
      newErrors.productId = "Please select a product";
    }

    if (!formData.quantity.trim()) {
      newErrors.quantity = "Quantity is required";
    } else if (isNaN(Number(formData.quantity)) || Number(formData.quantity) <= 0) {
      newErrors.quantity = "Quantity must be a positive number";
    } else {
      const selectedProduct = products.find(p => p.id === formData.productId);
      if (selectedProduct && formData.adjustmentType === "DECREASE") {
        if (Number(formData.quantity) > selectedProduct.currentStock) {
          newErrors.quantity = `Cannot decrease by more than current stock (${selectedProduct.currentStock})`;
        }
      }
    }

    if (!formData.reason.trim()) {
      newErrors.reason = "Reason is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const selectedProduct = products.find(p => p.id === formData.productId);
      const adjustmentQuantity = formData.adjustmentType === "INCREASE" 
        ? Number(formData.quantity)
        : -Number(formData.quantity);

      const adjustmentData = {
        productId: formData.productId,
        productName: selectedProduct?.name,
        quantity: adjustmentQuantity,
        reason: formData.reason.trim(),
        currentStock: selectedProduct?.currentStock,
        newStock: (selectedProduct?.currentStock || 0) + adjustmentQuantity,
      };

      await onSave(adjustmentData);
    } catch (error) {
      console.error("Error recording adjustment:", error);
      toast.error("Failed to record adjustment");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const productOptions = products.map(product => ({
    value: product.id,
    label: `${product.name} (Current: ${product.currentStock})`,
  }));

  const adjustmentTypeOptions = [
    { value: "INCREASE", label: "Increase Stock" },
    { value: "DECREASE", label: "Decrease Stock" },
  ];

  const reasonOptions = [
    { value: "Damaged goods", label: "Damaged goods" },
    { value: "Expired products", label: "Expired products" },
    { value: "Stock count correction", label: "Stock count correction" },
    { value: "Theft/Loss", label: "Theft/Loss" },
    { value: "Supplier return", label: "Supplier return" },
    { value: "Quality control", label: "Quality control" },
    { value: "Other", label: "Other (specify below)" },
  ];

  const selectedProduct = products.find(p => p.id === formData.productId);
  const adjustmentQuantity = Number(formData.quantity) || 0;
  const newStock = selectedProduct 
    ? selectedProduct.currentStock + (formData.adjustmentType === "INCREASE" ? adjustmentQuantity : -adjustmentQuantity)
    : 0;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <FormField
        label="Product"
        required
        error={errors.productId}
      >
        <Select
          options={productOptions}
          value={formData.productId}
          onChange={(value) => handleInputChange("productId", value)}
          placeholder="Select product"
          searchable
        />
      </FormField>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Adjustment Type"
          required
        >
          <Select
            options={adjustmentTypeOptions}
            value={formData.adjustmentType}
            onChange={(value) => handleInputChange("adjustmentType", value)}
          />
        </FormField>

        <FormField
          label="Quantity"
          required
          error={errors.quantity}
        >
          <Input
            type="number"
            placeholder="0"
            value={formData.quantity}
            onChange={(e) => handleInputChange("quantity", e.target.value)}
            min="1"
          />
        </FormField>
      </div>

      {selectedProduct && formData.quantity && (
        <div className="p-4 bg-secondary-50 rounded-lg">
          <h4 className="font-medium text-secondary-900 mb-2">Stock Preview</h4>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-secondary-600">Current Stock:</span>
              <div className="font-medium">{selectedProduct.currentStock}</div>
            </div>
            <div>
              <span className="text-secondary-600">Adjustment:</span>
              <div className={`font-medium ${formData.adjustmentType === "INCREASE" ? "text-success-600" : "text-error-600"}`}>
                {formData.adjustmentType === "INCREASE" ? "+" : "-"}{adjustmentQuantity}
              </div>
            </div>
            <div>
              <span className="text-secondary-600">New Stock:</span>
              <div className="font-medium">{newStock}</div>
            </div>
          </div>
        </div>
      )}

      <FormField
        label="Reason"
        required
        error={errors.reason}
      >
        <Select
          options={reasonOptions}
          value={formData.reason}
          onChange={(value) => handleInputChange("reason", value)}
          placeholder="Select reason"
        />
      </FormField>

      {formData.reason === "Other" && (
        <FormField
          label="Custom Reason"
          required
        >
          <Input
            placeholder="Please specify the reason"
            value={formData.reason === "Other" ? "" : formData.reason}
            onChange={(e) => handleInputChange("reason", e.target.value)}
          />
        </FormField>
      )}

      <div className="flex justify-end space-x-3 pt-4 border-t border-secondary-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
        >
          Record Adjustment
        </Button>
      </div>
    </form>
  );
};

export default InventoryAdjustmentModal;
