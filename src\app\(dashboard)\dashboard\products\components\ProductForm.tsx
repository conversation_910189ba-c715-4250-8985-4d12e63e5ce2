"use client";

import React, { useState, useEffect } from "react";
import { Button, Input, Select } from "@/components/ui";
import { FormField } from "@/components/forms";
import { useToastActions } from "@/components/ui";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
}

interface ProductFormProps {
  product?: Product | null;
  onSave: (productData: any) => void;
  onCancel: () => void;
}

interface FormData {
  name: string;
  description: string;
  price: string;
  stock: string;
  categoryId: string;
  warehouseId: string;
}

interface FormErrors {
  name?: string;
  price?: string;
  stock?: string;
  categoryId?: string;
  warehouseId?: string;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, onSave, onCancel }) => {
  const toast = useToastActions();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [warehouses, setWarehouses] = useState<Array<{ value: string; label: string }>>([]);
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    price: "",
    stock: "",
    categoryId: "",
    warehouseId: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    // Load categories and warehouses
    fetchCategories();
    fetchWarehouses();

    // Populate form if editing
    if (product) {
      setFormData({
        name: product.name,
        description: product.description || "",
        price: product.price.toString(),
        stock: product.stock.toString(),
        categoryId: product.category.id,
        warehouseId: product.warehouse.id,
      });
    }
  }, [product]);

  const fetchCategories = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data - replace with actual API response
      setCategories([
        { value: "1", label: "Makanan Ringan" },
        { value: "2", label: "Minuman" },
        { value: "3", label: "Makanan Instan" },
        { value: "4", label: "Bumbu Dapur" },
        { value: "5", label: "Produk Susu" },
      ]);
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to load categories");
    }
  };

  const fetchWarehouses = async () => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data - replace with actual API response
      setWarehouses([
        { value: "1", label: "Gudang Utama" },
        { value: "2", label: "Gudang Cabang" },
      ]);
    } catch (error) {
      console.error("Error fetching warehouses:", error);
      toast.error("Failed to load warehouses");
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Product name is required";
    }

    if (!formData.price.trim()) {
      newErrors.price = "Price is required";
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = "Price must be a valid positive number";
    }

    if (!formData.stock.trim()) {
      newErrors.stock = "Stock is required";
    } else if (isNaN(Number(formData.stock)) || Number(formData.stock) < 0) {
      newErrors.stock = "Stock must be a valid non-negative number";
    }

    if (!formData.categoryId) {
      newErrors.categoryId = "Category is required";
    }

    if (!formData.warehouseId) {
      newErrors.warehouseId = "Warehouse is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      // Find category and warehouse names
      const category = categories.find(c => c.value === formData.categoryId);
      const warehouse = warehouses.find(w => w.value === formData.warehouseId);

      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        price: Number(formData.price),
        stock: Number(formData.stock),
        categoryId: formData.categoryId,
        warehouseId: formData.warehouseId,
        category: {
          id: formData.categoryId,
          name: category?.label || "",
        },
        warehouse: {
          id: formData.warehouseId,
          name: warehouse?.label || "",
        },
      };

      await onSave(productData);
    } catch (error) {
      console.error("Error saving product:", error);
      toast.error("Failed to save product");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          label="Product Name"
          required
          error={errors.name}
        >
          <Input
            placeholder="Enter product name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
          />
        </FormField>

        <FormField
          label="Category"
          required
          error={errors.categoryId}
        >
          <Select
            options={categories}
            value={formData.categoryId}
            onChange={(value) => handleInputChange("categoryId", value)}
            placeholder="Select category"
          />
        </FormField>

        <FormField
          label="Price (IDR)"
          required
          error={errors.price}
        >
          <Input
            type="number"
            placeholder="0"
            value={formData.price}
            onChange={(e) => handleInputChange("price", e.target.value)}
            min="0"
            step="100"
          />
        </FormField>

        <FormField
          label="Stock Quantity"
          required
          error={errors.stock}
        >
          <Input
            type="number"
            placeholder="0"
            value={formData.stock}
            onChange={(e) => handleInputChange("stock", e.target.value)}
            min="0"
          />
        </FormField>

        <FormField
          label="Warehouse"
          required
          error={errors.warehouseId}
          className="md:col-span-2"
        >
          <Select
            options={warehouses}
            value={formData.warehouseId}
            onChange={(value) => handleInputChange("warehouseId", value)}
            placeholder="Select warehouse"
          />
        </FormField>
      </div>

      <FormField
        label="Description"
        helperText="Optional product description"
      >
        <textarea
          className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
          rows={3}
          placeholder="Enter product description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
        />
      </FormField>

      <div className="flex justify-end space-x-3 pt-4 border-t border-secondary-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          loading={loading}
        >
          {product ? "Update Product" : "Create Product"}
        </Button>
      </div>
    </form>
  );
};

export default ProductForm;
