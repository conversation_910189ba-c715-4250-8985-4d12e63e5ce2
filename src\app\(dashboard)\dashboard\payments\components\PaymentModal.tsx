"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Badge,
  Button,
  Select,
} from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";

interface Payment {
  id: string;
  orderId: string;
  customerName: string;
  amount: number;
  method: string;
  status: string;
  reference?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface PaymentModalProps {
  payment: Payment;
  onStatusUpdate: (paymentId: string, newStatus: string) => void;
  canUpdate: boolean;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  payment,
  onStatusUpdate,
  canUpdate,
}) => {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "warning" as const, label: "Pending" },
      COMPLETED: { variant: "success" as const, label: "Completed" },
      FAILED: { variant: "error" as const, label: "Failed" },
      REFUNDED: { variant: "secondary" as const, label: "Refunded" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "default" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getMethodBadge = (method: string) => {
    const methodConfig = {
      CASH: { variant: "success" as const, label: "Cash" },
      BANK_TRANSFER: { variant: "primary" as const, label: "Bank Transfer" },
      CREDIT: { variant: "warning" as const, label: "Credit" },
      DEBIT_CARD: { variant: "info" as const, label: "Debit Card" },
      E_WALLET: { variant: "secondary" as const, label: "E-Wallet" },
    };

    const config = methodConfig[method as keyof typeof methodConfig] || {
      variant: "default" as const,
      label: method,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const statusOptions = [
    { value: "COMPLETED", label: "Mark as Completed" },
    { value: "FAILED", label: "Mark as Failed" },
    { value: "REFUNDED", label: "Mark as Refunded" },
  ];

  return (
    <div className="space-y-6">
      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-600">Payment ID:</span>
                <span className="font-medium">{payment.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Order ID:</span>
                <span className="font-medium">{payment.orderId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Customer:</span>
                <span className="font-medium">{payment.customerName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Amount:</span>
                <span className="font-semibold text-lg">{formatCurrency(payment.amount)}</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-600">Method:</span>
                {getMethodBadge(payment.method)}
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Status:</span>
                {getStatusBadge(payment.status)}
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Created:</span>
                <span>{formatDate(new Date(payment.createdAt))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Updated:</span>
                <span>{formatDate(new Date(payment.updatedAt))}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Details */}
      {(payment.reference || payment.notes) && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {payment.reference && (
                <div className="flex justify-between">
                  <span className="text-secondary-600">Reference:</span>
                  <span className="font-medium">{payment.reference}</span>
                </div>
              )}
              {payment.notes && (
                <div>
                  <span className="text-secondary-600">Notes:</span>
                  <p className="mt-1 text-secondary-900">{payment.notes}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
              <div>
                <p className="font-medium">Payment Created</p>
                <p className="text-sm text-secondary-600">
                  {formatDate(new Date(payment.createdAt))}
                </p>
              </div>
            </div>
            
            {payment.status === "COMPLETED" && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-success-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Payment Completed</p>
                  <p className="text-sm text-secondary-600">
                    Payment has been successfully processed
                  </p>
                </div>
              </div>
            )}
            
            {payment.status === "FAILED" && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-error-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Payment Failed</p>
                  <p className="text-sm text-secondary-600">
                    Payment processing failed
                  </p>
                </div>
              </div>
            )}
            
            {payment.status === "REFUNDED" && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-secondary-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Payment Refunded</p>
                  <p className="text-sm text-secondary-600">
                    Payment has been refunded to customer
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Status Update */}
      {canUpdate && payment.status === "PENDING" && (
        <Card>
          <CardHeader>
            <CardTitle>Update Payment Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <span className="text-secondary-700">Change status to:</span>
              <Select
                options={statusOptions}
                value=""
                onChange={(value) => onStatusUpdate(payment.id, value)}
                placeholder="Select new status"
                className="w-48"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button variant="outline">
          Print Receipt
        </Button>
        <Button variant="outline">
          Send Email
        </Button>
        {payment.status === "COMPLETED" && (
          <Button variant="primary">
            Generate Invoice
          </Button>
        )}
      </div>
    </div>
  );
};

export default PaymentModal;
