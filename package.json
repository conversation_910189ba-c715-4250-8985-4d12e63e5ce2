{"name": "aidis", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/setup.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx scripts/seed.ts", "test": "jest", "test:watch": "jest --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@langchain/community": "^0.3.49", "@langchain/groq": "^0.2.3", "@prisma/client": "^6.12.0", "@supabase/supabase-js": "^2.52.1", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "faiss-node": "^0.5.1", "langchain": "^0.3.30", "next": "15.4.4", "next-auth": "^4.24.11", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "jest": "^29.7.0", "prisma": "^6.12.0", "tailwindcss": "^4", "typescript": "^5"}, "trustedDependencies": ["@prisma/client", "@prisma/engines", "@tailwindcss/oxide", "esbuild", "faiss-node", "prisma", "protobufjs", "sharp"]}