"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardContent,
  Table,
  Badge,
  Button,
  Select,
  EmptyState,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { formatDate } from "@/lib/utils";

interface StockAlert {
  id: string;
  productId: string;
  productName: string;
  currentStock: number;
  minStock: number;
  category: string;
  warehouse: string;
  alertType: "LOW_STOCK" | "OUT_OF_STOCK" | "OVERSTOCK" | "EXPIRING";
  severity: "HIGH" | "MEDIUM" | "LOW";
  createdAt: string;
}

interface StockAlertsProps {
  alerts: StockAlert[];
  onResolveAlert: (alertId: string) => void;
  onStockAdjustment: (product: any) => void;
}

const StockAlerts: React.FC<StockAlertsProps> = ({
  alerts,
  onResolveAlert,
  onStockAdjustment,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [severityFilter, setSeverityFilter] = useState("ALL");
  const [typeFilter, setTypeFilter] = useState("ALL");

  const getSeverityBadge = (severity: string) => {
    const severityConfig = {
      HIGH: { variant: "error" as const, label: "High" },
      MEDIUM: { variant: "warning" as const, label: "Medium" },
      LOW: { variant: "info" as const, label: "Low" },
    };

    const config = severityConfig[severity as keyof typeof severityConfig] || {
      variant: "default" as const,
      label: severity,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      LOW_STOCK: { variant: "warning" as const, label: "Low Stock" },
      OUT_OF_STOCK: { variant: "error" as const, label: "Out of Stock" },
      OVERSTOCK: { variant: "info" as const, label: "Overstock" },
      EXPIRING: { variant: "secondary" as const, label: "Expiring" },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || {
      variant: "default" as const,
      label: type,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "LOW_STOCK":
        return (
          <svg className="w-5 h-5 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case "OUT_OF_STOCK":
        return (
          <svg className="w-5 h-5 text-error-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case "OVERSTOCK":
        return (
          <svg className="w-5 h-5 text-info-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
          </svg>
        );
      case "EXPIRING":
        return (
          <svg className="w-5 h-5 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Filter alerts
  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         alert.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         alert.warehouse.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesSeverity = severityFilter === "ALL" || alert.severity === severityFilter;
    const matchesType = typeFilter === "ALL" || alert.alertType === typeFilter;
    
    return matchesSearch && matchesSeverity && matchesType;
  });

  // Group alerts by severity
  const alertsBySeverity = {
    HIGH: filteredAlerts.filter(a => a.severity === "HIGH"),
    MEDIUM: filteredAlerts.filter(a => a.severity === "MEDIUM"),
    LOW: filteredAlerts.filter(a => a.severity === "LOW"),
  };

  const severityOptions = [
    { value: "ALL", label: "All Severities" },
    { value: "HIGH", label: "High Priority" },
    { value: "MEDIUM", label: "Medium Priority" },
    { value: "LOW", label: "Low Priority" },
  ];

  const typeOptions = [
    { value: "ALL", label: "All Types" },
    { value: "LOW_STOCK", label: "Low Stock" },
    { value: "OUT_OF_STOCK", label: "Out of Stock" },
    { value: "OVERSTOCK", label: "Overstock" },
    { value: "EXPIRING", label: "Expiring" },
  ];

  const columns = [
    {
      key: "alert",
      title: "Alert",
      render: (_: any, alert: StockAlert) => (
        <div className="flex items-center space-x-3">
          {getAlertIcon(alert.alertType)}
          <div>
            <p className="font-medium text-secondary-900">{alert.productName}</p>
            <p className="text-sm text-secondary-600">{alert.category}</p>
          </div>
        </div>
      ),
    },
    {
      key: "stock",
      title: "Stock Level",
      render: (_: any, alert: StockAlert) => (
        <div>
          <p className="font-medium">
            {alert.currentStock} / {alert.minStock}
          </p>
          <p className="text-sm text-secondary-600">Current / Min</p>
        </div>
      ),
    },
    {
      key: "warehouse",
      title: "Warehouse",
      dataIndex: "warehouse",
    },
    {
      key: "alertType",
      title: "Type",
      dataIndex: "alertType",
      render: (value: string) => getTypeBadge(value),
    },
    {
      key: "severity",
      title: "Priority",
      dataIndex: "severity",
      render: (value: string) => getSeverityBadge(value),
    },
    {
      key: "createdAt",
      title: "Created",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, alert: StockAlert) => (
        <div className="flex space-x-2">
          <Button
            variant="primary"
            size="sm"
            onClick={() => onStockAdjustment({
              id: alert.productId,
              name: alert.productName,
              currentStock: alert.currentStock,
              minStock: alert.minStock,
            })}
          >
            Adjust
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onResolveAlert(alert.id)}
          >
            Resolve
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-error-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">High Priority</p>
                <p className="text-2xl font-bold text-error-600">
                  {alertsBySeverity.HIGH.length}
                </p>
              </div>
              <div className="w-10 h-10 bg-error-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-error-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-warning-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Medium Priority</p>
                <p className="text-2xl font-bold text-warning-600">
                  {alertsBySeverity.MEDIUM.length}
                </p>
              </div>
              <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-info-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Low Priority</p>
                <p className="text-2xl font-bold text-info-600">
                  {alertsBySeverity.LOW.length}
                </p>
              </div>
              <div className="w-10 h-10 bg-info-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-info-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-success-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Total Alerts</p>
                <p className="text-2xl font-bold text-success-600">
                  {filteredAlerts.length}
                </p>
              </div>
              <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search alerts..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Select
              options={severityOptions}
              value={severityFilter}
              onChange={setSeverityFilter}
              className="w-40"
            />
            <Select
              options={typeOptions}
              value={typeFilter}
              onChange={setTypeFilter}
              className="w-40"
            />
          </div>
        </CardContent>
      </Card>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Stock Alerts ({filteredAlerts.length})</CardTitle>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                Mark All Read
              </Button>
              <Button variant="outline" size="sm">
                Export Alerts
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredAlerts.length === 0 ? (
            <EmptyState
              title="No stock alerts"
              description="All inventory levels are within normal ranges"
              icon={
                <svg className="w-12 h-12 text-success-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />
          ) : (
            <Table
              columns={columns}
              data={filteredAlerts}
              emptyText="No alerts match your filters"
            />
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      {filteredAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => {
                  // Handle bulk reorder
                }}
              >
                <div className="text-left">
                  <p className="font-medium">Bulk Reorder</p>
                  <p className="text-sm text-secondary-600">
                    Reorder all low stock items
                  </p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => {
                  // Handle stock transfer
                }}
              >
                <div className="text-left">
                  <p className="font-medium">Stock Transfer</p>
                  <p className="text-sm text-secondary-600">
                    Transfer from other warehouses
                  </p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="justify-start h-auto p-4"
                onClick={() => {
                  // Handle alert settings
                }}
              >
                <div className="text-left">
                  <p className="font-medium">Alert Settings</p>
                  <p className="text-sm text-secondary-600">
                    Configure alert thresholds
                  </p>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default StockAlerts;
