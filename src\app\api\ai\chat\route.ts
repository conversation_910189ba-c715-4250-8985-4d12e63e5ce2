import { NextRequest } from "next/server";
import { distributorAgent } from "@/ai/agent";
import prisma from "@/db/client";
import {
  withAuth,
  successResponse,
  errorResponse,
  validateRequest,
  logApiRequest,
} from "@/lib/api-utils";
import { z } from "zod";

const chatSchema = z.object({
  message: z.string().min(1, "Message is required"),
  context: z.object({
    customerId: z.string().optional(),
    orderId: z.string().optional(),
    productId: z.string().optional(),
    conversationId: z.string().optional(),
  }).optional(),
});

// POST /api/ai/chat - AI-powered chat assistant
export const POST = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "AI_CHAT");

    const { message, context: chatContext } = await validateRequest(req, chatSchema);

    try {
      // Build context for AI
      let enhancedContext: any = {
        user: {
          id: user.id,
          name: user.name,
          role: user.role,
        },
      };

      // Add customer context if provided
      if (chatContext?.customerId) {
        const customer = await prisma.customer.findUnique({
          where: { id: chatContext.customerId },
          include: {
            orders: {
              orderBy: { createdAt: "desc" },
              take: 5,
              include: {
                items: {
                  include: {
                    product: {
                      select: { name: true, price: true },
                    },
                  },
                },
              },
            },
          },
        });
        
        if (customer) {
          enhancedContext.customer = {
            name: customer.name,
            email: customer.email,
            recentOrders: customer.orders.map(order => ({
              id: order.id,
              total: order.total,
              status: order.status,
              items: order.items.map(item => ({
                product: item.product.name,
                quantity: item.quantity,
                price: item.price,
              })),
            })),
          };
        }
      }

      // Add order context if provided
      if (chatContext?.orderId) {
        const order = await prisma.order.findUnique({
          where: { id: chatContext.orderId },
          include: {
            customer: true,
            items: {
              include: {
                product: true,
              },
            },
          },
        });
        
        if (order) {
          enhancedContext.order = {
            id: order.id,
            status: order.status,
            total: order.total,
            customer: order.customer.name,
            items: order.items.map(item => ({
              product: item.product.name,
              quantity: item.quantity,
              price: item.price,
            })),
          };
        }
      }

      // Add product context if provided
      if (chatContext?.productId) {
        const product = await prisma.product.findUnique({
          where: { id: chatContext.productId },
          include: {
            category: true,
            warehouse: true,
          },
        });
        
        if (product) {
          enhancedContext.product = {
            name: product.name,
            description: product.description,
            price: product.price,
            stock: product.stock,
            category: product.category.name,
            warehouse: product.warehouse.name,
          };
        }
      }

      // Get AI response
      const aiResponse = await distributorAgent.chatWithAgent(message, enhancedContext);

      // Save chat log for analytics
      try {
        await prisma.aISearchLog.create({
          data: {
            query: message,
            results: [{ type: "chat", response: aiResponse.response }],
            userId: user.id,
            duration: 0, // Chat duration not measured
          },
        });
      } catch (logError) {
        console.error("Error saving chat log:", logError);
        // Don't fail the request if logging fails
      }

      return successResponse({
        message: aiResponse.response,
        timestamp: aiResponse.timestamp,
        context: enhancedContext,
        conversationId: chatContext?.conversationId || `chat_${Date.now()}`,
      }, "Chat response generated successfully");

    } catch (error: any) {
      console.error("AI chat error:", error);
      return errorResponse("Chat failed: " + error.message, 500);
    }
  }
);

// GET /api/ai/chat/history - Get chat history for user
export const GET = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "GET_CHAT_HISTORY");

    try {
      const { searchParams } = new URL(req.url);
      const limit = parseInt(searchParams.get("limit") || "20");
      const page = parseInt(searchParams.get("page") || "1");

      const chatHistory = await prisma.aISearchLog.findMany({
        where: {
          userId: user.id,
          results: {
            path: "$[0].type",
            equals: "chat",
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      });

      const formattedHistory = chatHistory.map(log => ({
        id: log.id,
        query: log.query,
        response: (log.results as any)[0]?.response || "",
        timestamp: log.createdAt,
      }));

      return successResponse({
        history: formattedHistory,
        page,
        limit,
        total: chatHistory.length,
      }, "Chat history retrieved successfully");

    } catch (error: any) {
      console.error("Error getting chat history:", error);
      return errorResponse("Failed to get chat history: " + error.message, 500);
    }
  }
);
