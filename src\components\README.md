# UI Components Library

A comprehensive, reusable UI components library built with React, TypeScript, and Tailwind CSS for the AI Distributor Agent system.

## Overview

This library provides a complete set of production-ready components designed for modern web applications. All components are:

- **Fully Typed**: Built with TypeScript for better developer experience
- **Accessible**: Following WCAG guidelines and best practices
- **Responsive**: Mobile-first design with responsive breakpoints
- **Customizable**: Extensive theming and styling options
- **Consistent**: Unified design system across all components

## Components

### Core UI Components

#### Button
Versatile button component with multiple variants and states.

```tsx
import { Button } from "@/components/ui";

<Button variant="primary" size="md" loading={false}>
  Click me
</Button>
```

**Props:**
- `variant`: "primary" | "secondary" | "outline" | "ghost" | "destructive" | "success"
- `size`: "sm" | "md" | "lg" | "xl"
- `loading`: boolean
- `leftIcon`, `rightIcon`: React.ReactNode

#### Input
Form input component with validation and styling options.

```tsx
import { Input } from "@/components/ui";

<Input
  label="Email"
  type="email"
  placeholder="Enter your email"
  error="Invalid email"
  leftIcon={<EmailIcon />}
/>
```

#### Card
Container component for grouping related content.

```tsx
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui";

<Card variant="elevated">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    Card content goes here
  </CardContent>
</Card>
```

#### Table
Data table with sorting, pagination, and row actions.

```tsx
import { Table } from "@/components/ui";

const columns = [
  { key: "name", title: "Name", dataIndex: "name", sortable: true },
  { key: "email", title: "Email", dataIndex: "email" },
];

<Table
  columns={columns}
  data={data}
  loading={false}
  onRowClick={(record) => console.log(record)}
/>
```

#### Modal
Overlay component for dialogs and forms.

```tsx
import { Modal } from "@/components/ui";

<Modal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  title="Modal Title"
  size="md"
>
  Modal content
</Modal>
```

#### Select
Dropdown selection component with search and multi-select options.

```tsx
import { Select } from "@/components/ui";

<Select
  options={options}
  value={value}
  onChange={setValue}
  searchable
  clearable
  placeholder="Select an option"
/>
```

### Form Components

#### FormField
Wrapper component for form inputs with labels and validation.

```tsx
import { FormField } from "@/components/forms";

<FormField label="Username" required error="Username is required">
  <Input placeholder="Enter username" />
</FormField>
```

#### SearchInput
Specialized input for search functionality with debouncing.

```tsx
import { SearchInput } from "@/components/forms";

<SearchInput
  placeholder="Search products..."
  onSearch={handleSearch}
  debounceMs={300}
  loading={isSearching}
/>
```

### Layout Components

#### Sidebar
Navigation sidebar with collapsible menu items.

```tsx
import { Sidebar } from "@/components/layout";

const menuItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    href: "/dashboard",
    icon: <DashboardIcon />,
    requiredRoles: ["admin", "user"]
  }
];

<Sidebar
  items={menuItems}
  collapsed={collapsed}
  onToggle={() => setCollapsed(!collapsed)}
/>
```

#### Header
Application header with user menu and notifications.

```tsx
import { Header } from "@/components/layout";

<Header
  title="Dashboard"
  subtitle="Welcome back"
  showNotifications
  onMenuToggle={() => setSidebarOpen(!sidebarOpen)}
/>
```

### Utility Components

#### Badge
Small status indicators and labels.

```tsx
import { Badge } from "@/components/ui";

<Badge variant="success" size="md">Active</Badge>
<Badge variant="error" dot />
```

#### LoadingSpinner
Loading indicators for async operations.

```tsx
import { LoadingSpinner } from "@/components/ui";

<LoadingSpinner size="lg" text="Loading..." />
```

#### EmptyState
Placeholder for empty data states.

```tsx
import { EmptyState } from "@/components/ui";

<EmptyState
  title="No data found"
  description="Try adjusting your search criteria"
  action={{
    label: "Add Item",
    onClick: handleAddItem
  }}
/>
```

#### Stats
Statistics display component.

```tsx
import { Stats } from "@/components/ui";

const statsData = [
  {
    label: "Total Revenue",
    value: "$45,231",
    change: { value: 12, type: "increase" },
    icon: <RevenueIcon />,
    color: "success"
  }
];

<Stats stats={statsData} columns={4} />
```

#### Pagination
Page navigation component.

```tsx
import { Pagination } from "@/components/ui";

<Pagination
  currentPage={page}
  totalPages={totalPages}
  totalItems={total}
  itemsPerPage={limit}
  onPageChange={setPage}
  showSizeChanger
/>
```

### Notification System

#### Toast
Global notification system with multiple types.

```tsx
import { ToastProvider, useToastActions } from "@/components/ui";

// Wrap your app
<ToastProvider>
  <App />
</ToastProvider>

// Use in components
const toast = useToastActions();

toast.success("Operation completed!");
toast.error("Something went wrong");
toast.warning("Please check your input");
toast.info("New update available");
```

## Theming

The components use a comprehensive color system defined in Tailwind CSS:

- **Primary**: Main brand colors
- **Secondary**: Neutral grays
- **Success**: Green tones for positive actions
- **Warning**: Yellow/orange for cautions
- **Error**: Red tones for errors
- **Info**: Blue tones for information

## Best Practices

1. **Consistent Spacing**: Use the predefined spacing scale
2. **Color Usage**: Stick to the defined color palette
3. **Typography**: Use consistent font sizes and weights
4. **Accessibility**: Always provide proper labels and ARIA attributes
5. **Responsive Design**: Test components on different screen sizes
6. **Performance**: Use React.memo for expensive components

## Development

### Adding New Components

1. Create component in appropriate directory (`ui/`, `forms/`, `layout/`)
2. Export from index file
3. Add TypeScript interfaces
4. Include in demo page
5. Update documentation

### Testing Components

Visit `/components-demo` to see all components in action and test different states and configurations.

## Dependencies

- React 18+
- TypeScript 5+
- Tailwind CSS 3+
- Next.js 15+
- Tailwind Forms Plugin
- Tailwind Typography Plugin
