import React from "react";
import { cn } from "@/lib/utils";

export interface FormFieldProps {
  label?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  children: React.ReactNode;
  className?: string;
  labelClassName?: string;
  errorClassName?: string;
  helperClassName?: string;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  helperText,
  required = false,
  children,
  className,
  labelClassName,
  errorClassName,
  helperClassName,
}) => {
  return (
    <div className={cn("space-y-1", className)}>
      {label && (
        <label className={cn(
          "block text-sm font-medium text-secondary-700",
          labelClassName
        )}>
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}
      
      {children}
      
      {error && (
        <p className={cn("text-sm text-error-600", errorClassName)}>
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className={cn("text-sm text-secondary-500", helperClassName)}>
          {helperText}
        </p>
      )}
    </div>
  );
};

export { FormField };
