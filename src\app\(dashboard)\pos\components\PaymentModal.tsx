"use client";

import React, { useState } from "react";
import { Button, Input, Select } from "@/components/ui";
import { FormField } from "@/components/forms";
import { formatCurrency } from "@/lib/utils";
import { useToastActions } from "@/components/ui";

interface PaymentModalProps {
  total: number;
  onProcessPayment: (paymentData: any) => void;
  onCancel: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  total,
  onProcessPayment,
  onCancel,
}) => {
  const toast = useToastActions();
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("CASH");
  const [cashReceived, setCashReceived] = useState("");
  const [cardNumber, setCardNumber] = useState("");
  const [notes, setNotes] = useState("");

  const paymentMethods = [
    { value: "CASH", label: "Cash" },
    { value: "DEBIT_CARD", label: "Debit Card" },
    { value: "CREDIT_CARD", label: "Credit Card" },
    { value: "E_WALLET", label: "E-Wallet" },
    { value: "BANK_TRANSFER", label: "Bank Transfer" },
  ];

  const calculateChange = () => {
    if (paymentMethod !== "CASH" || !cashReceived) return 0;
    const received = parseFloat(cashReceived) || 0;
    return Math.max(0, received - total);
  };

  const isValidPayment = () => {
    switch (paymentMethod) {
      case "CASH":
        const received = parseFloat(cashReceived) || 0;
        return received >= total;
      case "DEBIT_CARD":
      case "CREDIT_CARD":
        return cardNumber.length >= 4; // Simplified validation
      case "E_WALLET":
      case "BANK_TRANSFER":
        return true; // Assume external validation
      default:
        return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isValidPayment()) {
      toast.error("Invalid payment details");
      return;
    }

    try {
      setLoading(true);

      const paymentData = {
        method: paymentMethod,
        amount: total,
        cashReceived: paymentMethod === "CASH" ? parseFloat(cashReceived) : total,
        change: calculateChange(),
        cardNumber: paymentMethod.includes("CARD") ? cardNumber : undefined,
        notes: notes.trim() || undefined,
        timestamp: new Date().toISOString(),
      };

      await onProcessPayment(paymentData);
    } catch (error) {
      console.error("Payment processing error:", error);
      toast.error("Payment processing failed");
    } finally {
      setLoading(false);
    }
  };

  const handleQuickCash = (amount: number) => {
    setCashReceived(amount.toString());
  };

  const quickCashAmounts = [
    Math.ceil(total / 1000) * 1000, // Round up to nearest thousand
    Math.ceil(total / 5000) * 5000, // Round up to nearest 5k
    Math.ceil(total / 10000) * 10000, // Round up to nearest 10k
    Math.ceil(total / 50000) * 50000, // Round up to nearest 50k
  ].filter((amount, index, arr) => arr.indexOf(amount) === index && amount > total);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Payment Summary */}
      <div className="bg-secondary-50 p-4 rounded-lg">
        <div className="text-center">
          <p className="text-sm text-secondary-600 mb-1">Total Amount</p>
          <p className="text-3xl font-bold text-primary-600">
            {formatCurrency(total)}
          </p>
        </div>
      </div>

      {/* Payment Method */}
      <FormField label="Payment Method" required>
        <Select
          options={paymentMethods}
          value={paymentMethod}
          onChange={setPaymentMethod}
        />
      </FormField>

      {/* Cash Payment */}
      {paymentMethod === "CASH" && (
        <div className="space-y-4">
          <FormField label="Cash Received" required>
            <Input
              type="number"
              placeholder="0"
              value={cashReceived}
              onChange={(e) => setCashReceived(e.target.value)}
              min={total}
              step="1000"
            />
          </FormField>

          {/* Quick Cash Buttons */}
          <div className="grid grid-cols-2 gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleQuickCash(total)}
            >
              Exact Amount
            </Button>
            {quickCashAmounts.slice(0, 3).map((amount) => (
              <Button
                key={amount}
                type="button"
                variant="outline"
                onClick={() => handleQuickCash(amount)}
              >
                {formatCurrency(amount)}
              </Button>
            ))}
          </div>

          {/* Change Calculation */}
          {cashReceived && (
            <div className="bg-success-50 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-success-700">Change:</span>
                <span className="text-xl font-bold text-success-600">
                  {formatCurrency(calculateChange())}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Card Payment */}
      {(paymentMethod === "DEBIT_CARD" || paymentMethod === "CREDIT_CARD") && (
        <div className="space-y-4">
          <FormField label="Card Number (Last 4 digits)" required>
            <Input
              type="text"
              placeholder="****"
              value={cardNumber}
              onChange={(e) => setCardNumber(e.target.value)}
              maxLength={4}
            />
          </FormField>

          <div className="bg-info-50 p-4 rounded-lg">
            <p className="text-info-700 text-sm">
              Please process the card payment on the terminal and enter the last 4 digits for reference.
            </p>
          </div>
        </div>
      )}

      {/* E-Wallet Payment */}
      {paymentMethod === "E_WALLET" && (
        <div className="bg-info-50 p-4 rounded-lg">
          <p className="text-info-700 text-sm">
            Please ask customer to complete payment via their e-wallet app (GoPay, OVO, DANA, etc.).
          </p>
        </div>
      )}

      {/* Bank Transfer */}
      {paymentMethod === "BANK_TRANSFER" && (
        <div className="bg-info-50 p-4 rounded-lg">
          <p className="text-info-700 text-sm">
            Please provide bank account details to customer for transfer payment.
          </p>
        </div>
      )}

      {/* Notes */}
      <FormField label="Notes (Optional)">
        <textarea
          className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
          rows={2}
          placeholder="Additional notes..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </FormField>

      {/* Payment Status */}
      <div className="flex items-center justify-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${isValidPayment() ? "bg-success-500" : "bg-error-500"}`} />
        <span className={`text-sm ${isValidPayment() ? "text-success-600" : "text-error-600"}`}>
          {isValidPayment() ? "Payment Valid" : "Payment Invalid"}
        </span>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3 pt-4 border-t border-secondary-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
          className="flex-1"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={!isValidPayment()}
          className="flex-1"
        >
          Process Payment
        </Button>
      </div>

      {/* Keyboard Shortcuts */}
      <div className="text-xs text-secondary-500 text-center space-x-4">
        <span>F1: Cash</span>
        <span>F2: Card</span>
        <span>F3: E-Wallet</span>
        <span>Enter: Process</span>
        <span>Esc: Cancel</span>
      </div>
    </form>
  );
};

export default PaymentModal;
