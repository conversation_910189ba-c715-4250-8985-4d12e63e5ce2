"use client";

import React, { useState } from "react";
import {
  Button,
  Input,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Modal,
  Table,
  Badge,
  Select,
  Pagination,
  LoadingSpinner,
  EmptyState,
  Stats,
  ToastProvider,
  useToastActions,
} from "@/components/ui";
import { FormField, SearchInput } from "@/components/forms";

const ComponentsDemo: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedValue, setSelectedValue] = useState("");
  const toast = useToastActions();

  // Sample data for table
  const tableData = [
    { id: 1, name: "Product A", price: 100, stock: 50, category: "Electronics" },
    { id: 2, name: "Product B", price: 200, stock: 30, category: "Clothing" },
    { id: 3, name: "Product C", price: 150, stock: 0, category: "Electronics" },
  ];

  const tableColumns = [
    { key: "name", title: "Name", dataIndex: "name", sortable: true },
    { key: "price", title: "Price", dataIndex: "price", sortable: true, render: (value: number) => `$${value}` },
    { key: "stock", title: "Stock", dataIndex: "stock", render: (value: number) => (
      <Badge variant={value > 0 ? "success" : "error"}>
        {value > 0 ? `${value} in stock` : "Out of stock"}
      </Badge>
    )},
    { key: "category", title: "Category", dataIndex: "category" },
  ];

  const selectOptions = [
    { value: "option1", label: "Option 1" },
    { value: "option2", label: "Option 2" },
    { value: "option3", label: "Option 3" },
  ];

  const statsData = [
    {
      label: "Total Revenue",
      value: "$45,231",
      change: { value: 12, type: "increase" as const, period: "from last month" },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: "success" as const,
    },
    {
      label: "Orders",
      value: "1,234",
      change: { value: 8, type: "increase" as const },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Customers",
      value: "567",
      change: { value: 3, type: "decrease" as const },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: "warning" as const,
    },
    {
      label: "Products",
      value: "89",
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: "info" as const,
    },
  ];

  return (
    <ToastProvider>
      <div className="p-8 space-y-8 max-w-7xl mx-auto">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">
            UI Components Demo
          </h1>
          <p className="text-secondary-600">
            Showcase of all available UI components in the AI Distributor Agent system.
          </p>
        </div>

        {/* Stats */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Statistics</h2>
          <Stats stats={statsData} />
        </section>

        {/* Buttons */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Buttons</h2>
          <Card>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="success">Success</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button loading>Loading</Button>
                  <Button disabled>Disabled</Button>
                  <Button leftIcon={<span>📧</span>}>With Icon</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Forms */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Form Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Input Fields</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <FormField label="Name" required>
                    <Input placeholder="Enter your name" />
                  </FormField>
                  <FormField label="Email" error="Invalid email format">
                    <Input type="email" placeholder="Enter your email" />
                  </FormField>
                  <FormField label="Search" helperText="Search for products">
                    <SearchInput
                      placeholder="Search products..."
                      onSearch={(query) => toast.info(`Searching for: ${query}`)}
                    />
                  </FormField>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Select & Other Inputs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <FormField label="Category">
                    <Select
                      options={selectOptions}
                      value={selectedValue}
                      onChange={setSelectedValue}
                      placeholder="Select a category"
                    />
                  </FormField>
                  <FormField label="Searchable Select">
                    <Select
                      options={selectOptions}
                      searchable
                      clearable
                      placeholder="Search and select"
                    />
                  </FormField>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Table */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Data Table</h2>
          <Card>
            <CardContent>
              <Table
                columns={tableColumns}
                data={tableData}
                onRowClick={(record) => toast.info(`Clicked on ${record.name}`)}
              />
            </CardContent>
          </Card>
        </section>

        {/* Badges */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Badges</h2>
          <Card>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Badge variant="default">Default</Badge>
                  <Badge variant="primary">Primary</Badge>
                  <Badge variant="success">Success</Badge>
                  <Badge variant="warning">Warning</Badge>
                  <Badge variant="error">Error</Badge>
                  <Badge variant="info">Info</Badge>
                </div>
                <div className="flex flex-wrap gap-2 items-center">
                  <Badge size="sm">Small</Badge>
                  <Badge size="md">Medium</Badge>
                  <Badge size="lg">Large</Badge>
                  <Badge dot variant="success" />
                  <span className="text-sm">Dot badge</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Pagination */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Pagination</h2>
          <Card>
            <CardContent>
              <Pagination
                currentPage={currentPage}
                totalPages={10}
                totalItems={100}
                itemsPerPage={10}
                onPageChange={setCurrentPage}
                showInfo
                showSizeChanger
              />
            </CardContent>
          </Card>
        </section>

        {/* Loading & Empty States */}
        <section>
          <h2 className="text-xl font-semibold mb-4">States</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Loading States</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <LoadingSpinner size="sm" text="Small spinner" />
                  <LoadingSpinner size="md" text="Medium spinner" />
                  <LoadingSpinner size="lg" text="Large spinner" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Empty State</CardTitle>
              </CardHeader>
              <CardContent>
                <EmptyState
                  title="No data found"
                  description="There are no items to display at the moment."
                  action={{
                    label: "Add Item",
                    onClick: () => toast.success("Add item clicked!"),
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Modal & Toast */}
        <section>
          <h2 className="text-xl font-semibold mb-4">Modal & Notifications</h2>
          <Card>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Button onClick={() => setShowModal(true)}>
                    Open Modal
                  </Button>
                  <Button onClick={() => toast.success("Success message!")}>
                    Success Toast
                  </Button>
                  <Button onClick={() => toast.error("Error message!")}>
                    Error Toast
                  </Button>
                  <Button onClick={() => toast.warning("Warning message!")}>
                    Warning Toast
                  </Button>
                  <Button onClick={() => toast.info("Info message!")}>
                    Info Toast
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Modal */}
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title="Example Modal"
          description="This is a demo modal with various content."
        >
          <div className="space-y-4">
            <p>This is the modal content. You can put any components here.</p>
            <Input placeholder="Example input in modal" />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowModal(false)}>
                Save
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </ToastProvider>
  );
};

export default ComponentsDemo;
