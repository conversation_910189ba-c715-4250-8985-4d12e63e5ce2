"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
  CardContent,
  Table,
  <PERSON>ton,
  Badge,
  Modal,
  Loading<PERSON>pinner,
  EmptyState,
  Pagination,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import CategoryForm from "./components/CategoryForm";

interface Category {
  id: string;
  name: string;
  description?: string;
  productCount: number;
  parentId?: string;
  parentName?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const CategoriesPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Modal states
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null);

  const canCreate = checkPermission(PERMISSIONS.PRODUCT_CREATE);
  const canUpdate = checkPermission(PERMISSIONS.PRODUCT_UPDATE);
  const canDelete = checkPermission(PERMISSIONS.PRODUCT_DELETE);

  useEffect(() => {
    fetchCategories();
  }, [currentPage, itemsPerPage, searchQuery]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API response
      const mockCategories: Category[] = [
        {
          id: "1",
          name: "Makanan Instan",
          description: "Produk makanan siap saji dan instan",
          productCount: 25,
          isActive: true,
          createdAt: "2024-01-15T10:30:00Z",
          updatedAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "2",
          name: "Minuman",
          description: "Berbagai jenis minuman kemasan",
          productCount: 18,
          isActive: true,
          createdAt: "2024-01-15T09:15:00Z",
          updatedAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "3",
          name: "Makanan Ringan",
          description: "Snack dan makanan ringan",
          productCount: 32,
          isActive: true,
          createdAt: "2024-01-14T16:45:00Z",
          updatedAt: "2024-01-14T16:45:00Z",
        },
        {
          id: "4",
          name: "Bumbu Dapur",
          description: "Bumbu dan rempah untuk memasak",
          productCount: 15,
          isActive: true,
          createdAt: "2024-01-14T14:20:00Z",
          updatedAt: "2024-01-14T14:20:00Z",
        },
        {
          id: "5",
          name: "Bahan Pokok",
          description: "Kebutuhan pokok sehari-hari",
          productCount: 12,
          isActive: true,
          createdAt: "2024-01-13T11:30:00Z",
          updatedAt: "2024-01-13T11:30:00Z",
        },
        {
          id: "6",
          name: "Produk Susu",
          description: "Susu dan produk olahan susu",
          productCount: 8,
          isActive: false,
          createdAt: "2024-01-12T08:15:00Z",
          updatedAt: "2024-01-12T08:15:00Z",
        },
      ];

      // Apply search filter
      let filteredCategories = mockCategories;
      if (searchQuery) {
        filteredCategories = filteredCategories.filter(category =>
          category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          category.description?.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      setCategories(filteredCategories);
      setTotalItems(filteredCategories.length);
      setTotalPages(Math.ceil(filteredCategories.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to fetch categories");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = () => {
    setEditingCategory(null);
    setShowCategoryModal(true);
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setShowCategoryModal(true);
  };

  const handleDeleteCategory = (category: Category) => {
    setDeletingCategory(category);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!deletingCategory) return;

    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCategories(prev => prev.filter(c => c.id !== deletingCategory.id));
      toast.success("Category deleted successfully");
      setShowDeleteModal(false);
      setDeletingCategory(null);
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    }
  };

  const handleCategorySave = async (categoryData: any) => {
    try {
      if (editingCategory) {
        // Update existing category
        setCategories(prev => 
          prev.map(c => 
            c.id === editingCategory.id 
              ? { ...c, ...categoryData, updatedAt: new Date().toISOString() }
              : c
          )
        );
        toast.success("Category updated successfully");
      } else {
        // Create new category
        const newCategory: Category = {
          id: Date.now().toString(),
          ...categoryData,
          productCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setCategories(prev => [newCategory, ...prev]);
        toast.success("Category created successfully");
      }
      
      setShowCategoryModal(false);
      setEditingCategory(null);
    } catch (error) {
      console.error("Error saving category:", error);
      toast.error("Failed to save category");
    }
  };

  const handleToggleStatus = async (categoryId: string, isActive: boolean) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCategories(prev => 
        prev.map(c => 
          c.id === categoryId 
            ? { ...c, isActive, updatedAt: new Date().toISOString() }
            : c
        )
      );
      
      toast.success(`Category ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error("Error updating category status:", error);
      toast.error("Failed to update category status");
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "success" : "secondary"}>
        {isActive ? "Active" : "Inactive"}
      </Badge>
    );
  };

  const columns = [
    {
      key: "name",
      title: "Category Name",
      dataIndex: "name",
      sortable: true,
    },
    {
      key: "description",
      title: "Description",
      dataIndex: "description",
      render: (value: string) => value || "-",
    },
    {
      key: "productCount",
      title: "Products",
      dataIndex: "productCount",
      render: (value: number) => (
        <Badge variant="info">{value} products</Badge>
      ),
      sortable: true,
    },
    {
      key: "isActive",
      title: "Status",
      dataIndex: "isActive",
      render: (value: boolean) => getStatusBadge(value),
    },
    {
      key: "createdAt",
      title: "Created",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
      sortable: true,
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, category: Category) => (
        <div className="flex space-x-2">
          {canUpdate && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleEditCategory(category)}
              >
                Edit
              </Button>
              <Button
                variant={category.isActive ? "secondary" : "success"}
                size="sm"
                onClick={() => handleToggleStatus(category.id, !category.isActive)}
              >
                {category.isActive ? "Deactivate" : "Activate"}
              </Button>
            </>
          )}
          {canDelete && category.productCount === 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteCategory(category)}
            >
              Delete
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Categories</h1>
          <p className="text-secondary-600">Manage product categories and organization</p>
        </div>
        {canCreate && (
          <Button onClick={handleCreateCategory}>
            Add Category
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search categories..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Categories Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Categories ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading categories..." />
            </div>
          ) : categories.length === 0 ? (
            <EmptyState
              title="No categories found"
              description="Get started by adding your first category"
              action={canCreate ? {
                label: "Add Category",
                onClick: handleCreateCategory,
              } : undefined}
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={categories}
                loading={loading}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Category Form Modal */}
      <Modal
        isOpen={showCategoryModal}
        onClose={() => {
          setShowCategoryModal(false);
          setEditingCategory(null);
        }}
        title={editingCategory ? "Edit Category" : "Add New Category"}
        size="md"
      >
        <CategoryForm
          category={editingCategory}
          onSave={handleCategorySave}
          onCancel={() => {
            setShowCategoryModal(false);
            setEditingCategory(null);
          }}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingCategory(null);
        }}
        title="Delete Category"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-secondary-700">
            Are you sure you want to delete "{deletingCategory?.name}"? This action cannot be undone.
          </p>
          {deletingCategory?.productCount && deletingCategory.productCount > 0 && (
            <div className="p-3 bg-warning-50 border border-warning-200 rounded-lg">
              <p className="text-warning-800 text-sm">
                This category contains {deletingCategory.productCount} products. 
                Please move or delete all products before deleting this category.
              </p>
            </div>
          )}
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteModal(false);
                setDeletingCategory(null);
              }}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDelete}
              disabled={deletingCategory?.productCount && deletingCategory.productCount > 0}
            >
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CategoriesPage;
