"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Card,
  CardContent,
  Badge,
  Button,
  Modal,
} from "@/components/ui";
import { formatCurrency } from "@/lib/utils";
import ProductDetailsModal from "./ProductDetailsModal";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
  images?: string[];
  tags?: string[];
  rating?: number;
  reviewCount?: number;
  createdAt: string;
}

interface ProductCardProps {
  product: Product;
  canUpdate: boolean;
  onAddToCart?: (product: Product) => void;
  onQuickView?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  canUpdate,
  onAddToCart,
  onQuickView,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [imageError, setImageError] = useState(false);

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="error" size="sm">Out of Stock</Badge>;
    } else if (stock <= 10) {
      return <Badge variant="warning" size="sm">Low Stock</Badge>;
    } else {
      return <Badge variant="success" size="sm">In Stock</Badge>;
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="w-4 h-4 text-yellow-400" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="half">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="transparent" />
            </linearGradient>
          </defs>
          <path
            fill="url(#half)"
            d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"
          />
        </svg>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-4 h-4 text-secondary-300" viewBox="0 0 20 20">
          <path
            fill="currentColor"
            d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"
          />
        </svg>
      );
    }

    return stars;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const defaultImage = "/api/placeholder/300/300";
  const productImage = imageError ? defaultImage : (product.images?.[0] || defaultImage);

  return (
    <>
      <Card className="group hover:shadow-lg transition-all duration-200 overflow-hidden">
        <div className="relative">
          {/* Product Image */}
          <div className="aspect-square relative overflow-hidden bg-secondary-50">
            <Image
              src={productImage}
              alt={product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              onError={handleImageError}
            />
            
            {/* Stock Badge */}
            <div className="absolute top-2 left-2">
              {getStockBadge(product.stock)}
            </div>

            {/* Quick Actions */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="flex flex-col space-y-1">
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-white/90 backdrop-blur-sm"
                  onClick={() => setShowDetails(true)}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </Button>
                {onAddToCart && product.stock > 0 && (
                  <Button
                    size="sm"
                    variant="primary"
                    className="bg-primary-600/90 backdrop-blur-sm"
                    onClick={() => onAddToCart(product)}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                    </svg>
                  </Button>
                )}
              </div>
            </div>
          </div>

          <CardContent className="p-4">
            {/* Category */}
            <div className="mb-2">
              <Badge variant="secondary" size="sm">
                {product.category.name}
              </Badge>
            </div>

            {/* Product Name */}
            <h3 className="font-semibold text-secondary-900 mb-2 line-clamp-2 min-h-[2.5rem]">
              {product.name}
            </h3>

            {/* Description */}
            {product.description && (
              <p className="text-sm text-secondary-600 mb-3 line-clamp-2">
                {product.description}
              </p>
            )}

            {/* Rating */}
            {product.rating && (
              <div className="flex items-center space-x-1 mb-3">
                <div className="flex items-center">
                  {renderStars(product.rating)}
                </div>
                <span className="text-sm text-secondary-600">
                  ({product.reviewCount || 0})
                </span>
              </div>
            )}

            {/* Price */}
            <div className="flex items-center justify-between mb-3">
              <div className="text-lg font-bold text-primary-600">
                {formatCurrency(product.price)}
              </div>
              <div className="text-sm text-secondary-600">
                Stock: {product.stock}
              </div>
            </div>

            {/* Tags */}
            {product.tags && product.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {product.tags.slice(0, 3).map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
                {product.tags.length > 3 && (
                  <span className="px-2 py-1 text-xs bg-secondary-100 text-secondary-700 rounded-full">
                    +{product.tags.length - 3}
                  </span>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="flex-1"
                onClick={() => setShowDetails(true)}
              >
                View Details
              </Button>
              {onAddToCart && (
                <Button
                  variant="primary"
                  size="sm"
                  className="flex-1"
                  disabled={product.stock === 0}
                  onClick={() => onAddToCart(product)}
                >
                  {product.stock === 0 ? "Out of Stock" : "Add to Cart"}
                </Button>
              )}
            </div>
          </CardContent>
        </div>
      </Card>

      {/* Product Details Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title="Product Details"
        size="lg"
      >
        <ProductDetailsModal
          product={product}
          onClose={() => setShowDetails(false)}
          onAddToCart={onAddToCart}
          canUpdate={canUpdate}
        />
      </Modal>
    </>
  );
};

export default ProductCard;
