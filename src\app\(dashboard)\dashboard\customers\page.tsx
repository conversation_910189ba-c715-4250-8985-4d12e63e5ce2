"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Table,
  <PERSON>ton,
  Badge,
  Modal,
  LoadingSpinner,
  EmptyState,
  Pagination,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import CustomerForm from "./components/CustomerForm";

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  createdAt: string;
}

const CustomersPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Modal states
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingCustomer, setDeletingCustomer] = useState<Customer | null>(null);

  const canCreate = checkPermission(PERMISSIONS.CUSTOMER_CREATE);
  const canUpdate = checkPermission(PERMISSIONS.CUSTOMER_UPDATE);
  const canDelete = checkPermission(PERMISSIONS.CUSTOMER_DELETE);

  useEffect(() => {
    fetchCustomers();
  }, [currentPage, itemsPerPage, searchQuery]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API response
      const mockCustomers: Customer[] = [
        {
          id: "1",
          name: "Toko Berkah Jaya",
          email: "<EMAIL>",
          phone: "081234567890",
          address: "Jl. Mawar No. 15, Jakarta Selatan",
          totalOrders: 25,
          totalSpent: 15750000,
          lastOrderDate: "2024-01-15T10:30:00Z",
          createdAt: "2023-06-15T10:30:00Z",
        },
        {
          id: "2",
          name: "Warung Maju Mundur",
          email: "<EMAIL>",
          phone: "081234567891",
          address: "Jl. Melati No. 22, Bandung",
          totalOrders: 18,
          totalSpent: 8900000,
          lastOrderDate: "2024-01-14T16:45:00Z",
          createdAt: "2023-08-20T14:20:00Z",
        },
        {
          id: "3",
          name: "Toko Sumber Rejeki",
          email: "<EMAIL>",
          phone: "081234567892",
          address: "Jl. Kenanga No. 8, Surabaya",
          totalOrders: 32,
          totalSpent: 22400000,
          lastOrderDate: "2024-01-13T09:15:00Z",
          createdAt: "2023-05-10T11:45:00Z",
        },
      ];

      setCustomers(mockCustomers);
      setTotalItems(mockCustomers.length);
      setTotalPages(Math.ceil(mockCustomers.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching customers:", error);
      toast.error("Failed to fetch customers");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomer = () => {
    setEditingCustomer(null);
    setShowCustomerModal(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowCustomerModal(true);
  };

  const handleDeleteCustomer = (customer: Customer) => {
    setDeletingCustomer(customer);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!deletingCustomer) return;

    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCustomers(prev => prev.filter(c => c.id !== deletingCustomer.id));
      toast.success("Customer deleted successfully");
      setShowDeleteModal(false);
      setDeletingCustomer(null);
    } catch (error) {
      console.error("Error deleting customer:", error);
      toast.error("Failed to delete customer");
    }
  };

  const handleCustomerSave = async (customerData: any) => {
    try {
      if (editingCustomer) {
        // Update existing customer
        setCustomers(prev => 
          prev.map(c => 
            c.id === editingCustomer.id 
              ? { ...c, ...customerData }
              : c
          )
        );
        toast.success("Customer updated successfully");
      } else {
        // Create new customer
        const newCustomer: Customer = {
          id: Date.now().toString(),
          ...customerData,
          totalOrders: 0,
          totalSpent: 0,
          createdAt: new Date().toISOString(),
        };
        setCustomers(prev => [newCustomer, ...prev]);
        toast.success("Customer created successfully");
      }
      
      setShowCustomerModal(false);
      setEditingCustomer(null);
    } catch (error) {
      console.error("Error saving customer:", error);
      toast.error("Failed to save customer");
    }
  };

  const getCustomerTypeBadge = (totalSpent: number) => {
    if (totalSpent >= 20000000) {
      return <Badge variant="success">VIP</Badge>;
    } else if (totalSpent >= 10000000) {
      return <Badge variant="primary">Premium</Badge>;
    } else if (totalSpent >= 5000000) {
      return <Badge variant="info">Regular</Badge>;
    } else {
      return <Badge variant="default">New</Badge>;
    }
  };

  const columns = [
    {
      key: "name",
      title: "Customer Name",
      dataIndex: "name",
      sortable: true,
    },
    {
      key: "email",
      title: "Email",
      dataIndex: "email",
    },
    {
      key: "phone",
      title: "Phone",
      dataIndex: "phone",
    },
    {
      key: "totalOrders",
      title: "Orders",
      dataIndex: "totalOrders",
      render: (value: number) => value.toLocaleString(),
      sortable: true,
    },
    {
      key: "totalSpent",
      title: "Total Spent",
      dataIndex: "totalSpent",
      render: (value: number) => formatCurrency(value),
      sortable: true,
    },
    {
      key: "type",
      title: "Type",
      render: (_: any, customer: Customer) => getCustomerTypeBadge(customer.totalSpent),
    },
    {
      key: "lastOrderDate",
      title: "Last Order",
      dataIndex: "lastOrderDate",
      render: (value: string) => value ? formatDate(new Date(value)) : "Never",
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, customer: Customer) => (
        <div className="flex space-x-2">
          {canUpdate && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEditCustomer(customer)}
            >
              Edit
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteCustomer(customer)}
            >
              Delete
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Customers</h1>
          <p className="text-secondary-600">Manage your customer database</p>
        </div>
        {canCreate && (
          <Button onClick={handleCreateCustomer}>
            Add Customer
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search customers..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Button variant="outline">
              Filter
            </Button>
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Customers ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading customers..." />
            </div>
          ) : customers.length === 0 ? (
            <EmptyState
              title="No customers found"
              description="Get started by adding your first customer"
              action={canCreate ? {
                label: "Add Customer",
                onClick: handleCreateCustomer,
              } : undefined}
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={customers}
                loading={loading}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Customer Form Modal */}
      <Modal
        isOpen={showCustomerModal}
        onClose={() => {
          setShowCustomerModal(false);
          setEditingCustomer(null);
        }}
        title={editingCustomer ? "Edit Customer" : "Add New Customer"}
        size="lg"
      >
        <CustomerForm
          customer={editingCustomer}
          onSave={handleCustomerSave}
          onCancel={() => {
            setShowCustomerModal(false);
            setEditingCustomer(null);
          }}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingCustomer(null);
        }}
        title="Delete Customer"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-secondary-700">
            Are you sure you want to delete "{deletingCustomer?.name}"? This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteModal(false);
                setDeletingCustomer(null);
              }}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CustomersPage;
