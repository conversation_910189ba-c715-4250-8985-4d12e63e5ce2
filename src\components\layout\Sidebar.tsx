import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { APP_CONFIG, USER_ROLES } from "@/config/constants";

export interface SidebarItem {
  id: string;
  label: string;
  href: string;
  icon: React.ReactNode;
  badge?: string | number;
  children?: SidebarItem[];
  requiredRoles?: string[];
  requiredPermissions?: string[];
}

export interface SidebarProps {
  items: SidebarItem[];
  collapsed?: boolean;
  onToggle?: () => void;
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
  items,
  collapsed = false,
  onToggle,
  className,
}) => {
  const pathname = usePathname();
  const { user, checkAnyRole, checkAnyPermission } = useAuth();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isItemVisible = (item: SidebarItem): boolean => {
    if (item.requiredRoles && !checkAnyRole(item.requiredRoles)) {
      return false;
    }
    if (item.requiredPermissions && !checkAnyPermission(item.requiredPermissions)) {
      return false;
    }
    return true;
  };

  const isItemActive = (item: SidebarItem): boolean => {
    if (pathname === item.href) return true;
    if (item.children) {
      return item.children.some(child => pathname === child.href);
    }
    return false;
  };

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    if (!isItemVisible(item)) return null;

    const isActive = isItemActive(item);
    const isExpanded = expandedItems.includes(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id}>
        <div
          className={cn(
            "flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer",
            level > 0 && "ml-4",
            isActive
              ? "bg-primary-100 text-primary-900 border-r-2 border-primary-600"
              : "text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900"
          )}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id);
            }
          }}
        >
          <Link
            href={hasChildren ? "#" : item.href}
            className="flex items-center flex-1 min-w-0"
            onClick={(e) => {
              if (hasChildren) {
                e.preventDefault();
              }
            }}
          >
            <span className="flex-shrink-0 w-5 h-5 mr-3">
              {item.icon}
            </span>
            {!collapsed && (
              <>
                <span className="truncate">{item.label}</span>
                {item.badge && (
                  <span className="ml-2 px-2 py-0.5 text-xs bg-primary-100 text-primary-800 rounded-full">
                    {item.badge}
                  </span>
                )}
              </>
            )}
          </Link>
          {!collapsed && hasChildren && (
            <svg
              className={cn(
                "w-4 h-4 transition-transform",
                isExpanded && "rotate-90"
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          )}
        </div>

        {!collapsed && hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-white border-r border-secondary-200 transition-all duration-300",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-secondary-200">
        {!collapsed && (
          <div>
            <h1 className="text-lg font-bold text-secondary-900">
              {APP_CONFIG.name.split(" - ")[1] || "Arta Boga"}
            </h1>
            <p className="text-xs text-secondary-500">
              AI Distributor Agent
            </p>
          </div>
        )}
        {onToggle && (
          <button
            onClick={onToggle}
            className="p-1 rounded-lg text-secondary-500 hover:text-secondary-700 hover:bg-secondary-100 transition-colors"
          >
            <svg
              className={cn(
                "w-5 h-5 transition-transform",
                collapsed && "rotate-180"
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
              />
            </svg>
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {items.map(item => renderSidebarItem(item))}
      </nav>

      {/* User Info */}
      {!collapsed && user && (
        <div className="p-4 border-t border-secondary-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-primary-800">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-secondary-900 truncate">
                {user.name}
              </p>
              <p className="text-xs text-secondary-500 capitalize">
                {user.role}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export { Sidebar };
