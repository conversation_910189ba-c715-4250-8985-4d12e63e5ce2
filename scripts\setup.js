#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up AI Distributor Agent - Arta Boga...\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
const envExamplePath = path.join(process.cwd(), '.env.example');

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    console.log('📋 Creating .env file from .env.example...');
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created successfully!\n');
  } else {
    console.log('❌ .env.example file not found!\n');
    process.exit(1);
  }
} else {
  console.log('✅ .env file already exists\n');
}

// Check if Prisma is set up
console.log('🗄️  Setting up database...');
try {
  // Generate Prisma client
  console.log('📦 Generating Prisma client...');
  execSync('bunx prisma generate', { stdio: 'inherit' });
  
  // Check if database exists and run migrations
  console.log('🔄 Running database migrations...');
  try {
    execSync('bunx prisma db push', { stdio: 'inherit' });
    console.log('✅ Database setup completed!\n');
  } catch (error) {
    console.log('⚠️  Database migration failed. Please check your DATABASE_URL in .env file.\n');
    console.log('You can run "bunx prisma db push" manually after setting up your database.\n');
  }
} catch (error) {
  console.log('❌ Prisma setup failed:', error.message);
  process.exit(1);
}

// Create necessary directories
console.log('📁 Creating project directories...');
const directories = [
  'src/components/ui',
  'src/components/forms',
  'src/components/layout',
  'src/app/api/auth',
  'src/app/api/products',
  'src/app/api/customers',
  'src/app/api/orders',
  'src/app/api/categories',
  'src/app/api/warehouses',
  'src/app/api/users',
  'src/app/api/inventory',
  'src/app/api/reports',
  'src/app/api/ai',
  'src/app/(auth)',
  'src/app/(dashboard)',
  'src/app/(public)',
  'src/hooks',
  'src/types',
  'src/utils',
  'public/images',
  'public/icons',
  'uploads',
  'logs',
];

directories.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`  ✅ Created ${dir}`);
  } else {
    console.log(`  ✅ ${dir} already exists`);
  }
});

console.log('\n📝 Creating initial configuration files...');

// Create gitignore additions
const gitignoreAdditions = `
# AI Distributor Agent specific
/uploads/*
!/uploads/.gitkeep
/logs/*
!/logs/.gitkeep
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
`;

const gitignorePath = path.join(process.cwd(), '.gitignore');
if (fs.existsSync(gitignorePath)) {
  const currentGitignore = fs.readFileSync(gitignorePath, 'utf8');
  if (!currentGitignore.includes('# AI Distributor Agent specific')) {
    fs.appendFileSync(gitignorePath, gitignoreAdditions);
    console.log('  ✅ Updated .gitignore');
  }
}

// Create placeholder files
const placeholders = [
  { path: 'uploads/.gitkeep', content: '' },
  { path: 'logs/.gitkeep', content: '' },
  { path: 'src/types/index.ts', content: '// Type definitions will be added here\nexport {};' },
];

placeholders.forEach(({ path: filePath, content }) => {
  const fullPath = path.join(process.cwd(), filePath);
  if (!fs.existsSync(fullPath)) {
    fs.writeFileSync(fullPath, content);
    console.log(`  ✅ Created ${filePath}`);
  }
});

console.log('\n🎉 Setup completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Update your .env file with your actual database and API credentials');
console.log('2. Run "bun dev" to start the development server');
console.log('3. Visit http://localhost:3000 to see your application');
console.log('\n💡 For production deployment:');
console.log('1. Set up your Supabase database');
console.log('2. Configure your environment variables');
console.log('3. Run "bun run build" to build for production');
console.log('\nHappy coding! 🚀');
