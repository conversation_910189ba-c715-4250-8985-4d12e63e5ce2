// Application Constants
export const APP_CONFIG = {
  name: process.env.APP_NAME || "AI Distributor Agent - Art<PERSON> Boga",
  version: process.env.APP_VERSION || "1.0.0",
  description: "Intelligent distributor management system with AI-powered search and automation",
  author: "Arta Boga",
} as const;

// User Roles and Permissions
export const USER_ROLES = {
  OWNER: "owner",
  ADMIN: "admin", 
  SALES: "sales",
  CASHIER: "cashier",
  CUSTOMER: "customer",
} as const;

export const PERMISSIONS = {
  // Product Management
  PRODUCT_CREATE: "product:create",
  PRODUCT_READ: "product:read",
  PRODUCT_UPDATE: "product:update",
  PRODUCT_DELETE: "product:delete",
  
  // Order Management
  ORDER_CREATE: "order:create",
  ORDER_READ: "order:read",
  ORDER_UPDATE: "order:update",
  ORDER_DELETE: "order:delete",
  
  // Customer Management
  CUSTOMER_CREATE: "customer:create",
  CUSTOMER_READ: "customer:read",
  CUSTOMER_UPDATE: "customer:update",
  CUSTOMER_DELETE: "customer:delete",
  
  // User Management
  USER_CREATE: "user:create",
  USER_READ: "user:read",
  USER_UPDATE: "user:update",
  USER_DELETE: "user:delete",
  
  // Inventory Management
  INVENTORY_READ: "inventory:read",
  INVENTORY_UPDATE: "inventory:update",
  
  // Reports
  REPORTS_READ: "reports:read",
  REPORTS_EXPORT: "reports:export",
  
  // System Administration
  SYSTEM_CONFIG: "system:config",
  SYSTEM_BACKUP: "system:backup",
} as const;

// Role-based permissions mapping
export const ROLE_PERMISSIONS = {
  [USER_ROLES.OWNER]: Object.values(PERMISSIONS),
  [USER_ROLES.ADMIN]: [
    PERMISSIONS.PRODUCT_CREATE,
    PERMISSIONS.PRODUCT_READ,
    PERMISSIONS.PRODUCT_UPDATE,
    PERMISSIONS.PRODUCT_DELETE,
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_READ,
    PERMISSIONS.ORDER_UPDATE,
    PERMISSIONS.CUSTOMER_CREATE,
    PERMISSIONS.CUSTOMER_READ,
    PERMISSIONS.CUSTOMER_UPDATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.INVENTORY_READ,
    PERMISSIONS.INVENTORY_UPDATE,
    PERMISSIONS.REPORTS_READ,
    PERMISSIONS.REPORTS_EXPORT,
  ],
  [USER_ROLES.SALES]: [
    PERMISSIONS.PRODUCT_READ,
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_READ,
    PERMISSIONS.ORDER_UPDATE,
    PERMISSIONS.CUSTOMER_CREATE,
    PERMISSIONS.CUSTOMER_READ,
    PERMISSIONS.CUSTOMER_UPDATE,
    PERMISSIONS.INVENTORY_READ,
  ],
  [USER_ROLES.CASHIER]: [
    PERMISSIONS.PRODUCT_READ,
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_READ,
    PERMISSIONS.CUSTOMER_READ,
    PERMISSIONS.INVENTORY_READ,
  ],
  [USER_ROLES.CUSTOMER]: [
    PERMISSIONS.PRODUCT_READ,
    PERMISSIONS.ORDER_READ,
  ],
} as const;

// Order Status
export const ORDER_STATUS = {
  PENDING: "PENDING",
  PROCESSING: "PROCESSING", 
  SHIPPED: "SHIPPED",
  DELIVERED: "DELIVERED",
  CANCELLED: "CANCELLED",
} as const;

// AI Configuration
export const AI_CONFIG = {
  maxTokens: 4000,
  temperature: 0.7,
  topK: 5,
  searchThreshold: 0.7,
  embeddingDimensions: 1536,
} as const;

// Pagination
export const PAGINATION = {
  defaultLimit: 10,
  maxLimit: 100,
} as const;

// File Upload
export const FILE_CONFIG = {
  maxSize: parseInt(process.env.MAX_FILE_SIZE || "5242880"), // 5MB
  allowedTypes: (process.env.ALLOWED_FILE_TYPES || "image/jpeg,image/png,image/webp").split(","),
  uploadPath: "/uploads",
} as const;

// API Routes
export const API_ROUTES = {
  AUTH: "/api/auth",
  PRODUCTS: "/api/products",
  CATEGORIES: "/api/categories",
  CUSTOMERS: "/api/customers",
  ORDERS: "/api/orders",
  USERS: "/api/users",
  WAREHOUSES: "/api/warehouses",
  INVENTORY: "/api/inventory",
  REPORTS: "/api/reports",
  AI_SEARCH: "/api/ai/search",
  AI_CHAT: "/api/ai/chat",
} as const;

// UI Constants
export const UI_CONFIG = {
  sidebarWidth: 280,
  headerHeight: 64,
  mobileBreakpoint: 768,
  tabletBreakpoint: 1024,
  desktopBreakpoint: 1280,
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];
