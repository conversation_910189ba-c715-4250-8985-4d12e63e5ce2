import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "./auth";
import { ApiResponse, PaginatedResponse } from "@/types";
import { ZodSchema } from "zod";

// API Response helpers
export function successResponse<T>(data: T, message?: string): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    message,
  });
}

export function errorResponse(error: string, status = 400): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error,
    },
    { status }
  );
}

export function paginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): NextResponse<PaginatedResponse<T>> {
  return NextResponse.json({
    data,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  });
}

// Authentication helper
export async function getAuthenticatedUser(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      throw new Error("Unauthorized");
    }

    return session.user;
  } catch (error) {
    throw new Error("Authentication failed");
  }
}

// Permission check helper
export function checkPermission(userPermissions: string[], requiredPermission: string) {
  if (!userPermissions.includes(requiredPermission)) {
    throw new Error(`Insufficient permissions. Required: ${requiredPermission}`);
  }
}

// Role check helper
export function checkRole(userRole: string, requiredRoles: string[]) {
  if (!requiredRoles.includes(userRole)) {
    throw new Error(`Insufficient role. Required: ${requiredRoles.join(" or ")}`);
  }
}

// Request validation helper
export async function validateRequest<T>(
  req: NextRequest,
  schema: ZodSchema<T>
): Promise<T> {
  try {
    const body = await req.json();
    const validatedData = schema.parse(body);
    return validatedData;
  } catch (error: any) {
    if (error.errors) {
      const errorMessages = error.errors.map((err: any) => 
        `${err.path.join('.')}: ${err.message}`
      ).join(', ');
      throw new Error(`Validation failed: ${errorMessages}`);
    }
    throw new Error("Invalid request data");
  }
}

// Query parameter helpers
export function getQueryParams(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  
  return {
    page: parseInt(searchParams.get("page") || "1"),
    limit: Math.min(parseInt(searchParams.get("limit") || "10"), 100),
    query: searchParams.get("query") || "",
    sortBy: searchParams.get("sortBy") || "createdAt",
    sortOrder: (searchParams.get("sortOrder") || "desc") as "asc" | "desc",
    filters: Object.fromEntries(
      Array.from(searchParams.entries()).filter(([key]) => 
        !["page", "limit", "query", "sortBy", "sortOrder"].includes(key)
      )
    ),
  };
}

// Error handling wrapper
export function withErrorHandling(handler: Function) {
  return async (req: NextRequest, context?: any) => {
    try {
      return await handler(req, context);
    } catch (error: any) {
      console.error("API Error:", error);
      
      if (error.message === "Unauthorized" || error.message === "Authentication failed") {
        return errorResponse("Unauthorized", 401);
      }
      
      if (error.message.startsWith("Insufficient permissions") || 
          error.message.startsWith("Insufficient role")) {
        return errorResponse(error.message, 403);
      }
      
      if (error.message.startsWith("Validation failed")) {
        return errorResponse(error.message, 400);
      }
      
      return errorResponse(
        process.env.NODE_ENV === "development" 
          ? error.message 
          : "Internal server error",
        500
      );
    }
  };
}

// Authentication middleware
export function withAuth(handler: Function, requiredPermissions?: string[], requiredRoles?: string[]) {
  return withErrorHandling(async (req: NextRequest, context?: any) => {
    const user = await getAuthenticatedUser(req);
    
    if (requiredPermissions) {
      for (const permission of requiredPermissions) {
        checkPermission(user.permissions, permission);
      }
    }
    
    if (requiredRoles) {
      checkRole(user.role, requiredRoles);
    }
    
    return handler(req, context, user);
  });
}

// CORS headers helper
export function withCors(response: NextResponse) {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
  return response;
}

// Rate limiting helper (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(identifier: string, maxRequests = 100, windowMs = 60000) {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < windowStart) {
      rateLimitMap.delete(key);
    }
  }
  
  const current = rateLimitMap.get(identifier);
  
  if (!current) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.resetTime < now) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}

// Logging helper
export function logApiRequest(req: NextRequest, user?: any, action?: string) {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const userInfo = user ? `${user.name} (${user.email})` : "Anonymous";
  
  console.log(`[${timestamp}] ${method} ${url} - User: ${userInfo} - Action: ${action || "N/A"}`);
}

// Search helper
export function buildSearchQuery(query: string, searchFields: string[]) {
  if (!query) return {};
  
  return {
    OR: searchFields.map(field => ({
      [field]: {
        contains: query,
        mode: "insensitive" as const,
      },
    })),
  };
}

// Sorting helper
export function buildSortQuery(sortBy: string, sortOrder: "asc" | "desc") {
  return {
    [sortBy]: sortOrder,
  };
}

// Date range filter helper
export function buildDateRangeFilter(startDate?: string, endDate?: string) {
  const filter: any = {};
  
  if (startDate) {
    filter.gte = new Date(startDate);
  }
  
  if (endDate) {
    filter.lte = new Date(endDate);
  }
  
  return Object.keys(filter).length > 0 ? filter : undefined;
}
