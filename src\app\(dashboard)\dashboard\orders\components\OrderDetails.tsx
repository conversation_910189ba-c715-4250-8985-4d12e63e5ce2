"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  <PERSON>ge,
  <PERSON>ton,
  Select,
  Table,
} from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";

interface OrderItem {
  id: string;
  productName: string;
  quantity: number;
  price: number;
}

interface Order {
  id: string;
  customerName: string;
  customerEmail: string;
  status: string;
  total: number;
  discount?: number;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

interface OrderDetailsProps {
  order: Order;
  onStatusUpdate: (orderId: string, newStatus: string) => void;
  canUpdate: boolean;
}

const OrderDetails: React.FC<OrderDetailsProps> = ({
  order,
  onStatusUpdate,
  canUpdate,
}) => {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "warning" as const, label: "Pending" },
      PROCESSING: { variant: "info" as const, label: "Processing" },
      SHIPPED: { variant: "primary" as const, label: "Shipped" },
      DELIVERED: { variant: "success" as const, label: "Delivered" },
      CANCELLED: { variant: "error" as const, label: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "default" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const statusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "PROCESSING", label: "Processing" },
    { value: "SHIPPED", label: "Shipped" },
    { value: "DELIVERED", label: "Delivered" },
    { value: "CANCELLED", label: "Cancel" },
  ];

  const itemColumns = [
    {
      key: "productName",
      title: "Product",
      dataIndex: "productName",
    },
    {
      key: "quantity",
      title: "Quantity",
      dataIndex: "quantity",
      render: (value: number) => value.toLocaleString(),
    },
    {
      key: "price",
      title: "Unit Price",
      dataIndex: "price",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "total",
      title: "Total",
      render: (_: any, item: OrderItem) => formatCurrency(item.quantity * item.price),
    },
  ];

  const subtotal = order.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
  const discountAmount = order.discount || 0;
  const finalTotal = subtotal - discountAmount;

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-600">Order ID:</span>
                <span className="font-medium">{order.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Status:</span>
                {getStatusBadge(order.status)}
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Order Date:</span>
                <span>{formatDate(new Date(order.createdAt))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Last Updated:</span>
                <span>{formatDate(new Date(order.updatedAt))}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-600">Name:</span>
                <span className="font-medium">{order.customerName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Email:</span>
                <span>{order.customerEmail}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Items */}
      <Card>
        <CardHeader>
          <CardTitle>Order Items</CardTitle>
        </CardHeader>
        <CardContent>
          <Table
            columns={itemColumns}
            data={order.items}
            emptyText="No items in this order"
          />
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-secondary-600">Subtotal:</span>
              <span>{formatCurrency(subtotal)}</span>
            </div>
            {discountAmount > 0 && (
              <div className="flex justify-between text-success-600">
                <span>Discount:</span>
                <span>-{formatCurrency(discountAmount)}</span>
              </div>
            )}
            <div className="border-t pt-3">
              <div className="flex justify-between text-lg font-semibold">
                <span>Total:</span>
                <span>{formatCurrency(finalTotal)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Update */}
      {canUpdate && order.status !== "DELIVERED" && order.status !== "CANCELLED" && (
        <Card>
          <CardHeader>
            <CardTitle>Update Order Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <span className="text-secondary-700">Change status to:</span>
              <Select
                options={statusOptions.filter(option => option.value !== order.status)}
                value=""
                onChange={(value) => onStatusUpdate(order.id, value)}
                placeholder="Select new status"
                className="w-48"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Order Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Order Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
              <div>
                <p className="font-medium">Order Created</p>
                <p className="text-sm text-secondary-600">
                  {formatDate(new Date(order.createdAt))}
                </p>
              </div>
            </div>
            
            {order.status !== "PENDING" && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-info-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Order Processing</p>
                  <p className="text-sm text-secondary-600">
                    Order is being processed
                  </p>
                </div>
              </div>
            )}
            
            {(order.status === "SHIPPED" || order.status === "DELIVERED") && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-primary-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Order Shipped</p>
                  <p className="text-sm text-secondary-600">
                    Order has been shipped to customer
                  </p>
                </div>
              </div>
            )}
            
            {order.status === "DELIVERED" && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-success-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Order Delivered</p>
                  <p className="text-sm text-secondary-600">
                    Order has been delivered successfully
                  </p>
                </div>
              </div>
            )}
            
            {order.status === "CANCELLED" && (
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-error-600 rounded-full"></div>
                <div>
                  <p className="font-medium">Order Cancelled</p>
                  <p className="text-sm text-secondary-600">
                    Order has been cancelled
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button variant="outline">
          Print Invoice
        </Button>
        <Button variant="outline">
          Send Email
        </Button>
        {order.status === "DELIVERED" && (
          <Button variant="primary">
            Generate Receipt
          </Button>
        )}
      </div>
    </div>
  );
};

export default OrderDetails;
