"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Select,
  Input,
  Badge,
} from "@/components/ui";
import { FormField } from "@/components/forms";

interface SearchFilters {
  category: string;
  warehouse: string;
  priceRange: {
    min: number;
    max: number;
  };
  stockStatus: string;
  rating: number;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

interface ProductFiltersProps {
  filters: SearchFilters;
  onFilterChange: (filters: Partial<SearchFilters>) => void;
  onClearFilters: () => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
}) => {
  const [categories, setCategories] = useState<Array<{ value: string; label: string }>>([]);
  const [warehouses, setWarehouses] = useState<Array<{ value: string; label: string }>>([]);
  const [priceRange, setPriceRange] = useState({
    min: filters.priceRange.min.toString(),
    max: filters.priceRange.max.toString(),
  });

  useEffect(() => {
    fetchFilterOptions();
  }, []);

  useEffect(() => {
    setPriceRange({
      min: filters.priceRange.min.toString(),
      max: filters.priceRange.max.toString(),
    });
  }, [filters.priceRange]);

  const fetchFilterOptions = async () => {
    try {
      // Simulate API calls - replace with actual API endpoints
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data
      setCategories([
        { value: "ALL", label: "All Categories" },
        { value: "1", label: "Makanan Instan" },
        { value: "2", label: "Minuman" },
        { value: "3", label: "Makanan Ringan" },
        { value: "4", label: "Bumbu Dapur" },
        { value: "5", label: "Bahan Pokok" },
        { value: "6", label: "Produk Susu" },
        { value: "7", label: "Frozen Food" },
      ]);

      setWarehouses([
        { value: "ALL", label: "All Warehouses" },
        { value: "1", label: "Gudang Utama" },
        { value: "2", label: "Gudang Cabang" },
        { value: "3", label: "Gudang Frozen" },
      ]);
    } catch (error) {
      console.error("Error fetching filter options:", error);
    }
  };

  const stockStatusOptions = [
    { value: "ALL", label: "All Stock Status" },
    { value: "IN_STOCK", label: "In Stock" },
    { value: "LOW_STOCK", label: "Low Stock" },
    { value: "OUT_OF_STOCK", label: "Out of Stock" },
  ];

  const ratingOptions = [
    { value: "0", label: "All Ratings" },
    { value: "4", label: "4+ Stars" },
    { value: "3", label: "3+ Stars" },
    { value: "2", label: "2+ Stars" },
    { value: "1", label: "1+ Stars" },
  ];

  const handlePriceRangeChange = (field: "min" | "max", value: string) => {
    const newPriceRange = { ...priceRange, [field]: value };
    setPriceRange(newPriceRange);
    
    // Only update filters if both values are valid numbers
    const minValue = Number(newPriceRange.min) || 0;
    const maxValue = Number(newPriceRange.max) || 1000000;
    
    if (!isNaN(minValue) && !isNaN(maxValue) && minValue <= maxValue) {
      onFilterChange({
        priceRange: { min: minValue, max: maxValue }
      });
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <svg
          key={i}
          className={`w-4 h-4 ${i <= rating ? "text-yellow-400" : "text-secondary-300"} fill-current`}
          viewBox="0 0 20 20"
        >
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }
    return stars;
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.category !== "ALL") count++;
    if (filters.warehouse !== "ALL") count++;
    if (filters.priceRange.min > 0 || filters.priceRange.max < 1000000) count++;
    if (filters.stockStatus !== "ALL") count++;
    if (filters.rating > 0) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className="sticky top-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <Badge variant="primary" size="sm">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-secondary-600 hover:text-secondary-900"
            >
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Category Filter */}
        <FormField label="Category">
          <Select
            options={categories}
            value={filters.category}
            onChange={(value) => onFilterChange({ category: value })}
          />
        </FormField>

        {/* Warehouse Filter */}
        <FormField label="Warehouse">
          <Select
            options={warehouses}
            value={filters.warehouse}
            onChange={(value) => onFilterChange({ warehouse: value })}
          />
        </FormField>

        {/* Price Range Filter */}
        <FormField label="Price Range (IDR)">
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Min"
                value={priceRange.min}
                onChange={(e) => handlePriceRangeChange("min", e.target.value)}
                min="0"
              />
              <Input
                type="number"
                placeholder="Max"
                value={priceRange.max}
                onChange={(e) => handlePriceRangeChange("max", e.target.value)}
                min="0"
              />
            </div>
            <div className="text-xs text-secondary-600">
              Current: {Number(priceRange.min).toLocaleString()} - {Number(priceRange.max).toLocaleString()}
            </div>
          </div>
        </FormField>

        {/* Stock Status Filter */}
        <FormField label="Stock Status">
          <Select
            options={stockStatusOptions}
            value={filters.stockStatus}
            onChange={(value) => onFilterChange({ stockStatus: value })}
          />
        </FormField>

        {/* Rating Filter */}
        <FormField label="Minimum Rating">
          <div className="space-y-2">
            <Select
              options={ratingOptions}
              value={filters.rating.toString()}
              onChange={(value) => onFilterChange({ rating: Number(value) })}
            />
            {filters.rating > 0 && (
              <div className="flex items-center space-x-1">
                {renderStars(filters.rating)}
                <span className="text-sm text-secondary-600">& up</span>
              </div>
            )}
          </div>
        </FormField>

        {/* Quick Filter Buttons */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-secondary-900">Quick Filters</h4>
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onFilterChange({ stockStatus: "LOW_STOCK" })}
            >
              <svg className="w-4 h-4 mr-2 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              Low Stock Items
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onFilterChange({ rating: 4 })}
            >
              <svg className="w-4 h-4 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
              </svg>
              Top Rated (4+)
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onFilterChange({ 
                priceRange: { min: 0, max: 10000 }
              })}
            >
              <svg className="w-4 h-4 mr-2 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              Under 10K
            </Button>
          </div>
        </div>

        {/* Active Filters Summary */}
        {activeFiltersCount > 0 && (
          <div className="pt-4 border-t border-secondary-200">
            <h4 className="text-sm font-medium text-secondary-900 mb-2">Active Filters</h4>
            <div className="space-y-1 text-sm text-secondary-600">
              {filters.category !== "ALL" && (
                <div>Category: {categories.find(c => c.value === filters.category)?.label}</div>
              )}
              {filters.warehouse !== "ALL" && (
                <div>Warehouse: {warehouses.find(w => w.value === filters.warehouse)?.label}</div>
              )}
              {(filters.priceRange.min > 0 || filters.priceRange.max < 1000000) && (
                <div>
                  Price: {filters.priceRange.min.toLocaleString()} - {filters.priceRange.max.toLocaleString()}
                </div>
              )}
              {filters.stockStatus !== "ALL" && (
                <div>Stock: {stockStatusOptions.find(s => s.value === filters.stockStatus)?.label}</div>
              )}
              {filters.rating > 0 && (
                <div>Rating: {filters.rating}+ stars</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProductFilters;
