import prisma from "@/db/client";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { PromptTemplate } from "@langchain/core/prompts";
import {
  chatModel,
  createVectorStore,
  embeddings,
  searchSimilar,
} from "./config";

// AI Agent class for handling distributor operations
export class DistributorAIAgent {
  private vectorStore: any = null;
  private isInitialized = false;

  constructor() {
    this.initializeAgent();
  }

  // Initialize the AI agent with product data
  async initializeAgent() {
    try {
      console.log("Initializing AI Distributor Agent...");
      await this.buildProductVectorStore();
      this.isInitialized = true;
      console.log("AI Agent initialized successfully");
    } catch (error) {
      console.error("Failed to initialize AI agent:", error);
      throw error;
    }
  }

  // Build vector store from product data
  async buildProductVectorStore() {
    try {
      const products = await prisma.product.findMany({
        include: {
          category: true,
          warehouse: true,
        },
      });

      if (products.length === 0) {
        console.warn("No products found for vector store initialization");
        return;
      }

      const texts = products.map(
        (product) =>
          `${product.name} ${product.description || ""} ${
            product.category.name
          } ${product.warehouse.name}`
      );

      const metadata = products.map((product) => ({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        stock: product.stock,
        category: product.category.name,
        warehouse: product.warehouse.name,
      }));

      this.vectorStore = await createVectorStore(texts, metadata);

      // Save product search data to database
      await this.saveProductSearchData(products, texts);
    } catch (error) {
      console.error("Error building product vector store:", error);
      throw error;
    }
  }

  // Save product search data to database
  async saveProductSearchData(products: any[], searchTexts: string[]) {
    try {
      for (let i = 0; i < products.length; i++) {
        const product = products[i];
        const searchText = searchTexts[i];

        // Generate embedding for the search text
        const embedding = await embeddings.embedQuery(searchText);

        await prisma.productSearch.upsert({
          where: { id: product.id },
          update: {
            searchText,
            embedding: JSON.stringify(embedding),
            metadata: {
              category: product.category.name,
              warehouse: product.warehouse.name,
              lastUpdated: new Date().toISOString(),
            },
          },
          create: {
            productId: product.id,
            searchText,
            embedding: JSON.stringify(embedding),
            metadata: {
              category: product.category.name,
              warehouse: product.warehouse.name,
              lastUpdated: new Date().toISOString(),
            },
          },
        });
      }
    } catch (error) {
      console.error("Error saving product search data:", error);
    }
  }

  // Intelligent product search
  async searchProducts(query: string, userId?: string) {
    const startTime = Date.now();

    try {
      if (!this.isInitialized || !this.vectorStore) {
        await this.initializeAgent();
      }

      // Perform vector similarity search
      const similarProducts = await searchSimilar(this.vectorStore, query);

      // Get detailed product information
      const productIds = similarProducts.map(
        (result: any) => result.metadata.id
      );
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
        include: {
          category: true,
          warehouse: true,
        },
      });

      // Sort products by similarity score
      const sortedProducts = products.sort((a, b) => {
        const aIndex = productIds.indexOf(a.id);
        const bIndex = productIds.indexOf(b.id);
        return aIndex - bIndex;
      });

      const duration = Date.now() - startTime;

      // Log search for analytics
      if (userId) {
        await this.logSearch(query, sortedProducts, userId, duration);
      }

      return {
        products: sortedProducts,
        totalFound: sortedProducts.length,
        searchTime: duration,
        query,
      };
    } catch (error) {
      console.error("Error in product search:", error);
      throw new Error("Failed to search products");
    }
  }

  // Log search queries for analytics
  async logSearch(
    query: string,
    results: any[],
    userId: string,
    duration: number
  ) {
    try {
      await prisma.aISearchLog.create({
        data: {
          query,
          results: results.map((p) => ({ id: p.id, name: p.name, score: 0 })),
          userId,
          duration,
        },
      });
    } catch (error) {
      console.error("Error logging search:", error);
    }
  }

  // Generate AI-powered product recommendations
  async getProductRecommendations(customerId: string, limit = 5) {
    try {
      // Get customer's order history
      const customerOrders = await prisma.order.findMany({
        where: { customerId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  category: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 10,
      });

      if (customerOrders.length === 0) {
        // Return popular products for new customers
        return await this.getPopularProducts(limit);
      }

      // Extract purchased product categories and names
      const purchasedProducts = customerOrders.flatMap((order) =>
        order.items.map((item) => item.product)
      );

      const categories = [
        ...new Set(purchasedProducts.map((p) => p.category.name)),
      ];
      const productNames = purchasedProducts.map((p) => p.name).join(", ");

      // Generate recommendation query
      const recommendationQuery = `Similar products to: ${productNames} in categories: ${categories.join(
        ", "
      )}`;

      const recommendations = await this.searchProducts(recommendationQuery);

      // Filter out already purchased products
      const purchasedIds = new Set(purchasedProducts.map((p) => p.id));
      const filteredRecommendations = recommendations.products
        .filter((p) => !purchasedIds.has(p.id))
        .slice(0, limit);

      return {
        products: filteredRecommendations,
        reason: "Based on your purchase history",
        categories,
      };
    } catch (error) {
      console.error("Error generating recommendations:", error);
      return await this.getPopularProducts(limit);
    }
  }

  // Get popular products as fallback
  async getPopularProducts(limit = 5) {
    try {
      const popularProducts = await prisma.product.findMany({
        include: {
          category: true,
          warehouse: true,
          orderItems: true,
        },
        orderBy: {
          orderItems: {
            _count: "desc",
          },
        },
        take: limit,
      });

      return {
        products: popularProducts,
        reason: "Popular products",
        categories: [],
      };
    } catch (error) {
      console.error("Error getting popular products:", error);
      return { products: [], reason: "Error", categories: [] };
    }
  }

  // AI-powered chat for customer support
  async chatWithAgent(message: string, context?: any) {
    try {
      const prompts = `You are an AI assistant for Arta Boga, a distributor company. 
      You help customers find products, answer questions about orders, and provide support.
      
      Available context: ${context ? JSON.stringify(context) : "None"}
      
      Guidelines:
      - Be helpful and professional
      - Provide accurate product information
      - Help with order inquiries
      - Suggest relevant products when appropriate
      - Use Indonesian language when appropriate
      - Keep responses concise but informative`;

      const systemPromptText = prompts;

      const prompt = PromptTemplate.fromTemplate(`
        System: {systemPrompt}
        
        Human: {message}
        
        Assistant: I'll help you with that. Let me provide you with the information you need.
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());

      const response = await chain.invoke({
        systemPrompt: systemPromptText,
        message,
      });

      return {
        response,
        timestamp: new Date().toISOString(),
        context,
      };
    } catch (error) {
      console.error("Error in AI chat:", error);
      return {
        response:
          "Maaf, saya mengalami kesulitan teknis. Silakan coba lagi nanti.",
        timestamp: new Date().toISOString(),
        error: true,
      };
    }
  }

  // Refresh vector store when products are updated
  async refreshVectorStore() {
    try {
      console.log("Refreshing product vector store...");
      await this.buildProductVectorStore();
      console.log("Vector store refreshed successfully");
    } catch (error) {
      console.error("Error refreshing vector store:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const distributorAgent = new DistributorAIAgent();
