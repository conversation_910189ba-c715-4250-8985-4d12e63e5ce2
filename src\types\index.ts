import { DefaultSession, DefaultUser } from "next-auth";
import { DefaultJWT } from "next-auth/jwt";

// Extend NextAuth types
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      role: string;
      permissions: string[];
    } & DefaultSession["user"];
  }

  interface User extends DefaultUser {
    role: string;
    permissions: string[];
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    role: string;
    permissions: string[];
  }
}

// Application types
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  filters?: Record<string, any>;
}

export interface ProductWithDetails {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
    address: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderWithDetails {
  id: string;
  customer: {
    id: string;
    name: string;
    email: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
  };
  status: string;
  items: {
    id: string;
    product: {
      id: string;
      name: string;
      price: number;
    };
    quantity: number;
    price: number;
  }[];
  total: number;
  discount?: number;
  promotion?: {
    id: string;
    name: string;
    type: string;
    value: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  isRead: boolean;
  userId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface InventoryAnalysis {
  totalProducts: number;
  lowStock: number;
  outOfStock: number;
  overstock: number;
  lowStockProducts: Array<{
    id: string;
    name: string;
    stock: number;
    category: string;
    warehouse: string;
  }>;
  outOfStockProducts: Array<{
    id: string;
    name: string;
    category: string;
    warehouse: string;
  }>;
}

export interface AISearchResult {
  products: ProductWithDetails[];
  totalFound: number;
  searchTime: number;
  query: string;
}

export interface PromotionRecommendation {
  recommendations: string;
  salesData: any;
  slowMovingProducts: any[];
  topCustomers: any[];
  generatedAt: string;
}

export {};
