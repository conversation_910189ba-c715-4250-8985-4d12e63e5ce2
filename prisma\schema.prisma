generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String                 @id @default(uuid())
  email                 String                 @unique
  name                  String
  password              String
  role                  Role                   @relation(fields: [roleId], references: [id])
  roleId                String
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  orders                Order[]
  notifications         Notification[]
  inventoryTransactions InventoryTransaction[]
  aiSearchLogs          AISearchLog[]
}

model Role {
  id          String   @id @default(uuid())
  name        String   @unique
  permissions String[]
  users       User[]
}

model Product {
  id                    String                 @id @default(uuid())
  name                  String
  description           String?
  price                 Float
  stock                 Int
  category              Category               @relation(fields: [categoryId], references: [id])
  categoryId            String
  warehouse             Warehouse              @relation(fields: [warehouseId], references: [id])
  warehouseId           String
  orderItems            OrderItem[]
  promotions            Promotion[]            @relation("PromotionProducts")
  inventoryTransactions InventoryTransaction[]
  productSearches       ProductSearch[]
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
}

model Category {
  id         String      @id @default(uuid())
  name       String      @unique
  products   Product[]
  promotions Promotion[] @relation("PromotionCategories")
}

model Warehouse {
  id       String    @id @default(uuid())
  name     String
  address  String
  products Product[]
}

model Customer {
  id        String   @id @default(uuid())
  name      String
  email     String   @unique
  phone     String?
  address   String?
  orders    Order[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Order {
  id          String      @id @default(uuid())
  customer    Customer    @relation(fields: [customerId], references: [id])
  customerId  String
  user        User        @relation(fields: [userId], references: [id])
  userId      String
  status      OrderStatus @default(PENDING)
  items       OrderItem[]
  total       Float
  discount    Float?      @default(0)
  promotion   Promotion?  @relation(fields: [promotionId], references: [id])
  promotionId String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

model OrderItem {
  id        String   @id @default(uuid())
  order     Order    @relation(fields: [orderId], references: [id])
  orderId   String
  product   Product  @relation(fields: [productId], references: [id])
  productId String
  quantity  Int
  price     Float
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

model Promotion {
  id          String           @id @default(uuid())
  name        String
  description String?
  type        PromotionType
  value       Float            // Percentage or fixed amount
  minAmount   Float?           // Minimum order amount
  maxDiscount Float?           // Maximum discount amount
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean          @default(true)
  usageLimit  Int?             // Maximum number of uses
  usageCount  Int              @default(0)
  categories  Category[]       @relation("PromotionCategories")
  products    Product[]        @relation("PromotionProducts")
  orders      Order[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
}

model Notification {
  id        String           @id @default(uuid())
  title     String
  message   String
  type      NotificationType
  priority  NotificationPriority @default(MEDIUM)
  isRead    Boolean          @default(false)
  userId    String?
  user      User?            @relation(fields: [userId], references: [id])
  metadata  Json?            // Additional data
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
}

model InventoryTransaction {
  id          String                    @id @default(uuid())
  product     Product                   @relation(fields: [productId], references: [id])
  productId   String
  type        InventoryTransactionType
  quantity    Int
  reason      String?
  reference   String?                   // Order ID, adjustment ID, etc.
  user        User                      @relation(fields: [userId], references: [id])
  userId      String
  createdAt   DateTime                  @default(now())
}

model ProductSearch {
  id          String   @id @default(uuid())
  productId   String
  product     Product  @relation(fields: [productId], references: [id])
  searchText  String   // Processed search text for AI
  embedding   String?  // Vector embedding as JSON string
  metadata    Json?    // Additional search metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([productId])
  @@index([searchText])
}

model AISearchLog {
  id        String   @id @default(uuid())
  query     String
  results   Json     // Search results
  userId    String?
  user      User?    @relation(fields: [userId], references: [id])
  duration  Int      // Search duration in milliseconds
  createdAt DateTime @default(now())

  @@index([userId])
  @@index([createdAt])
}

model SystemSettings {
  id    String @id @default(uuid())
  key   String @unique
  value String
  type  String // 'string', 'number', 'boolean', 'json'
}

enum PromotionType {
  PERCENTAGE
  FIXED_AMOUNT
  BUY_X_GET_Y
  FREE_SHIPPING
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  PROMOTION
  INVENTORY
  ORDER
  SYSTEM
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum InventoryTransactionType {
  PURCHASE
  SALE
  ADJUSTMENT
  RETURN
  DAMAGE
  TRANSFER
}
