import React from "react";
import { cn } from "@/lib/utils";
import { Card } from "./Card";

export interface StatItem {
  label: string;
  value: string | number;
  change?: {
    value: number;
    type: "increase" | "decrease";
    period?: string;
  };
  icon?: React.ReactNode;
  color?: "primary" | "success" | "warning" | "error" | "info";
}

export interface StatsProps {
  stats: StatItem[];
  className?: string;
  columns?: 1 | 2 | 3 | 4;
}

const Stats: React.FC<StatsProps> = ({
  stats,
  className,
  columns = 4,
}) => {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  };

  const colors = {
    primary: {
      bg: "bg-primary-50",
      text: "text-primary-600",
      icon: "text-primary-500",
    },
    success: {
      bg: "bg-success-50",
      text: "text-success-600",
      icon: "text-success-500",
    },
    warning: {
      bg: "bg-warning-50",
      text: "text-warning-600",
      icon: "text-warning-500",
    },
    error: {
      bg: "bg-error-50",
      text: "text-error-600",
      icon: "text-error-500",
    },
    info: {
      bg: "bg-blue-50",
      text: "text-blue-600",
      icon: "text-blue-500",
    },
  };

  return (
    <div className={cn(
      "grid gap-4",
      gridCols[columns],
      className
    )}>
      {stats.map((stat, index) => {
        const color = stat.color || "primary";
        const colorClasses = colors[color];

        return (
          <Card key={index} variant="elevated" padding="lg">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-secondary-600 mb-1">
                  {stat.label}
                </p>
                <p className="text-2xl font-bold text-secondary-900">
                  {stat.value}
                </p>
                {stat.change && (
                  <div className="flex items-center mt-2">
                    <svg
                      className={cn(
                        "w-4 h-4 mr-1",
                        stat.change.type === "increase"
                          ? "text-success-500 rotate-0"
                          : "text-error-500 rotate-180"
                      )}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 17l9.2-9.2M17 17V7H7"
                      />
                    </svg>
                    <span
                      className={cn(
                        "text-sm font-medium",
                        stat.change.type === "increase"
                          ? "text-success-600"
                          : "text-error-600"
                      )}
                    >
                      {Math.abs(stat.change.value)}%
                    </span>
                    {stat.change.period && (
                      <span className="text-sm text-secondary-500 ml-1">
                        {stat.change.period}
                      </span>
                    )}
                  </div>
                )}
              </div>
              {stat.icon && (
                <div className={cn(
                  "w-12 h-12 rounded-lg flex items-center justify-center",
                  colorClasses.bg
                )}>
                  <div className={cn("w-6 h-6", colorClasses.icon)}>
                    {stat.icon}
                  </div>
                </div>
              )}
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export { Stats };
