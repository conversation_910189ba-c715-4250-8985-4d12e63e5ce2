"use client";

import React from "react";
import ProductCard from "./ProductCard";
import { useToastActions } from "@/components/ui";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
  images?: string[];
  tags?: string[];
  rating?: number;
  reviewCount?: number;
  createdAt: string;
}

interface ProductGridViewProps {
  products: Product[];
  canUpdate: boolean;
}

const ProductGridView: React.FC<ProductGridViewProps> = ({
  products,
  canUpdate,
}) => {
  const toast = useToastActions();

  const handleAddToCart = (product: Product) => {
    // Simulate adding to cart - replace with actual cart logic
    toast.success(`${product.name} added to cart`);
  };

  const handleQuickView = (product: Product) => {
    // Handle quick view - this could open a modal or navigate to product details
    console.log("Quick view:", product);
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          canUpdate={canUpdate}
          onAddToCart={handleAddToCart}
          onQuickView={handleQuickView}
        />
      ))}
    </div>
  );
};

export default ProductGridView;
