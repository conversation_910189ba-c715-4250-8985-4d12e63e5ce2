import { chatModel } from "./config";
import { PromptTemplate } from "@langchain/core/prompts";
import { StringOutputParser } from "@langchain/core/output_parsers";
import prisma from "@/db/client";
import { PromotionType } from "@prisma/client";

export class AIPromotionManager {
  // Generate AI-powered promotion recommendations
  async generatePromotionRecommendations() {
    try {
      // Get sales data for analysis
      const salesData = await this.getSalesAnalytics();
      const slowMovingProducts = await this.getSlowMovingProducts();
      const topCustomers = await this.getTopCustomers();

      const prompt = PromptTemplate.fromTemplate(`
        As an AI marketing manager for Arta Boga distributor, analyze the following data and recommend promotional strategies:

        Sales Analytics:
        {salesData}

        Slow Moving Products:
        {slowMovingProducts}

        Top Customers:
        {topCustomers}

        Generate specific promotion recommendations including:
        1. Product-specific promotions for slow-moving items
        2. Customer loyalty programs
        3. Seasonal promotion ideas
        4. Bundle deals and cross-selling opportunities
        5. Discount strategies with optimal pricing
        6. Target customer segments for each promotion

        Format as actionable promotion campaigns with clear objectives and expected outcomes.
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());
      
      const recommendations = await chain.invoke({
        salesData: JSON.stringify(salesData, null, 2),
        slowMovingProducts: JSON.stringify(slowMovingProducts, null, 2),
        topCustomers: JSON.stringify(topCustomers, null, 2),
      });

      return {
        recommendations,
        salesData,
        slowMovingProducts,
        topCustomers,
        generatedAt: new Date().toISOString(),
      };

    } catch (error) {
      console.error("Error generating promotion recommendations:", error);
      throw new Error("Failed to generate promotion recommendations");
    }
  }

  // Get sales analytics for promotion planning
  async getSalesAnalytics() {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const orders = await prisma.order.findMany({
        where: {
          createdAt: { gte: thirtyDaysAgo },
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  category: true,
                },
              },
            },
          },
          customer: true,
        },
      });

      const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
      const totalOrders = orders.length;
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // Category performance
      const categoryPerformance: { [key: string]: { revenue: number; quantity: number } } = {};
      
      orders.forEach(order => {
        order.items.forEach(item => {
          const category = item.product.category.name;
          if (!categoryPerformance[category]) {
            categoryPerformance[category] = { revenue: 0, quantity: 0 };
          }
          categoryPerformance[category].revenue += item.price * item.quantity;
          categoryPerformance[category].quantity += item.quantity;
        });
      });

      return {
        totalRevenue,
        totalOrders,
        averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
        categoryPerformance,
        period: "Last 30 days",
      };

    } catch (error) {
      console.error("Error getting sales analytics:", error);
      return null;
    }
  }

  // Identify slow-moving products
  async getSlowMovingProducts(limit = 10) {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const products = await prisma.product.findMany({
        include: {
          category: true,
          orderItems: {
            where: {
              order: {
                createdAt: { gte: thirtyDaysAgo },
              },
            },
          },
        },
      });

      const slowMovingProducts = products
        .map(product => ({
          id: product.id,
          name: product.name,
          category: product.category.name,
          stock: product.stock,
          price: product.price,
          soldQuantity: product.orderItems.reduce((sum, item) => sum + item.quantity, 0),
          turnoverRate: product.stock > 0 ? 
            product.orderItems.reduce((sum, item) => sum + item.quantity, 0) / product.stock : 0,
        }))
        .filter(product => product.turnoverRate < 0.1 && product.stock > 5)
        .sort((a, b) => a.turnoverRate - b.turnoverRate)
        .slice(0, limit);

      return slowMovingProducts;

    } catch (error) {
      console.error("Error getting slow-moving products:", error);
      return [];
    }
  }

  // Get top customers for targeted promotions
  async getTopCustomers(limit = 10) {
    try {
      const customers = await prisma.customer.findMany({
        include: {
          orders: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days
              },
            },
          },
        },
      });

      const topCustomers = customers
        .map(customer => ({
          id: customer.id,
          name: customer.name,
          email: customer.email,
          totalOrders: customer.orders.length,
          totalSpent: customer.orders.reduce((sum, order) => sum + order.total, 0),
          averageOrderValue: customer.orders.length > 0 ? 
            customer.orders.reduce((sum, order) => sum + order.total, 0) / customer.orders.length : 0,
          lastOrderDate: customer.orders.length > 0 ? 
            Math.max(...customer.orders.map(o => o.createdAt.getTime())) : null,
        }))
        .filter(customer => customer.totalOrders > 0)
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, limit);

      return topCustomers;

    } catch (error) {
      console.error("Error getting top customers:", error);
      return [];
    }
  }

  // Create automated promotion based on AI recommendations
  async createAutomatedPromotion(productIds: string[], promotionData: any) {
    try {
      const promotion = await prisma.promotion.create({
        data: {
          name: promotionData.name,
          description: promotionData.description,
          type: promotionData.type as PromotionType,
          value: promotionData.value,
          minAmount: promotionData.minAmount,
          maxDiscount: promotionData.maxDiscount,
          startDate: new Date(promotionData.startDate),
          endDate: new Date(promotionData.endDate),
          usageLimit: promotionData.usageLimit,
          products: {
            connect: productIds.map(id => ({ id })),
          },
        },
      });

      // Create notification about new promotion
      await prisma.notification.create({
        data: {
          title: "New Promotion Created",
          message: `AI-generated promotion "${promotion.name}" has been created`,
          type: "PROMOTION",
          priority: "MEDIUM",
          metadata: {
            promotionId: promotion.id,
            productCount: productIds.length,
            aiGenerated: true,
          },
        },
      });

      return promotion;

    } catch (error) {
      console.error("Error creating automated promotion:", error);
      throw new Error("Failed to create automated promotion");
    }
  }

  // Analyze promotion effectiveness
  async analyzePromotionEffectiveness(promotionId: string) {
    try {
      const promotion = await prisma.promotion.findUnique({
        where: { id: promotionId },
        include: {
          products: true,
          orders: {
            include: {
              items: {
                include: {
                  product: true,
                },
              },
            },
          },
        },
      });

      if (!promotion) {
        throw new Error("Promotion not found");
      }

      const totalOrders = promotion.orders.length;
      const totalRevenue = promotion.orders.reduce((sum, order) => sum + order.total, 0);
      const totalDiscount = promotion.orders.reduce((sum, order) => sum + (order.discount || 0), 0);
      const conversionRate = promotion.usageCount / (promotion.usageLimit || 1);

      const prompt = PromptTemplate.fromTemplate(`
        Analyze the effectiveness of this promotion campaign:

        Promotion Details:
        - Name: {promotionName}
        - Type: {promotionType}
        - Value: {promotionValue}
        - Duration: {startDate} to {endDate}
        - Usage: {usageCount} / {usageLimit}

        Performance Metrics:
        - Total Orders: {totalOrders}
        - Total Revenue: {totalRevenue}
        - Total Discount Given: {totalDiscount}
        - Conversion Rate: {conversionRate}%
        - Products Included: {productCount}

        Provide:
        1. Overall performance assessment
        2. ROI analysis
        3. Customer response evaluation
        4. Recommendations for future promotions
        5. Optimization suggestions
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());
      
      const analysis = await chain.invoke({
        promotionName: promotion.name,
        promotionType: promotion.type,
        promotionValue: promotion.value,
        startDate: promotion.startDate.toISOString().split('T')[0],
        endDate: promotion.endDate.toISOString().split('T')[0],
        usageCount: promotion.usageCount,
        usageLimit: promotion.usageLimit || 'Unlimited',
        totalOrders,
        totalRevenue,
        totalDiscount,
        conversionRate: (conversionRate * 100).toFixed(2),
        productCount: promotion.products.length,
      });

      return {
        promotion: {
          id: promotion.id,
          name: promotion.name,
          type: promotion.type,
          value: promotion.value,
        },
        metrics: {
          totalOrders,
          totalRevenue,
          totalDiscount,
          conversionRate: parseFloat((conversionRate * 100).toFixed(2)),
          roi: totalRevenue > 0 ? ((totalRevenue - totalDiscount) / totalDiscount) * 100 : 0,
        },
        analysis,
      };

    } catch (error) {
      console.error("Error analyzing promotion effectiveness:", error);
      throw new Error("Failed to analyze promotion effectiveness");
    }
  }

  // Generate personalized promotions for customers
  async generatePersonalizedPromotions(customerId: string) {
    try {
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          orders: {
            include: {
              items: {
                include: {
                  product: {
                    include: {
                      category: true,
                    },
                  },
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
        },
      });

      if (!customer) {
        throw new Error("Customer not found");
      }

      const purchaseHistory = customer.orders.flatMap(order => 
        order.items.map(item => ({
          product: item.product,
          quantity: item.quantity,
          price: item.price,
          orderDate: order.createdAt,
        }))
      );

      const favoriteCategories = this.getFavoriteCategories(purchaseHistory);
      const averageOrderValue = customer.orders.length > 0 ? 
        customer.orders.reduce((sum, order) => sum + order.total, 0) / customer.orders.length : 0;

      const prompt = PromptTemplate.fromTemplate(`
        Create personalized promotion recommendations for this customer:

        Customer Profile:
        - Name: {customerName}
        - Total Orders: {totalOrders}
        - Average Order Value: {averageOrderValue}
        - Favorite Categories: {favoriteCategories}
        - Recent Purchases: {recentPurchases}

        Generate 3-5 personalized promotion ideas that would appeal to this customer based on their purchase history and preferences.
        Include specific discount percentages, product recommendations, and compelling messaging.
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());
      
      const personalizedPromotions = await chain.invoke({
        customerName: customer.name,
        totalOrders: customer.orders.length,
        averageOrderValue: averageOrderValue.toFixed(2),
        favoriteCategories: favoriteCategories.join(', '),
        recentPurchases: JSON.stringify(purchaseHistory.slice(0, 5), null, 2),
      });

      return {
        customerId,
        customerName: customer.name,
        personalizedPromotions,
        favoriteCategories,
        averageOrderValue: parseFloat(averageOrderValue.toFixed(2)),
        totalOrders: customer.orders.length,
      };

    } catch (error) {
      console.error("Error generating personalized promotions:", error);
      throw new Error("Failed to generate personalized promotions");
    }
  }

  // Helper function to get favorite categories
  private getFavoriteCategories(purchaseHistory: any[]) {
    const categoryCount: { [key: string]: number } = {};
    
    purchaseHistory.forEach(purchase => {
      const category = purchase.product.category.name;
      categoryCount[category] = (categoryCount[category] || 0) + purchase.quantity;
    });

    return Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([category]) => category);
  }
}

// Export singleton instance
export const aiPromotionManager = new AIPromotionManager();
