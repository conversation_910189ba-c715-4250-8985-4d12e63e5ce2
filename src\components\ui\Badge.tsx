import React from "react";
import { cn } from "@/lib/utils";

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: "default" | "primary" | "secondary" | "success" | "warning" | "error" | "info";
  size?: "sm" | "md" | "lg";
  dot?: boolean;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = "default", size = "md", dot = false, children, ...props }, ref) => {
    const baseStyles = "inline-flex items-center font-medium rounded-full transition-colors";
    
    const variants = {
      default: "bg-secondary-100 text-secondary-800",
      primary: "bg-primary-100 text-primary-800",
      secondary: "bg-secondary-100 text-secondary-800",
      success: "bg-success-100 text-success-800",
      warning: "bg-warning-100 text-warning-800",
      error: "bg-error-100 text-error-800",
      info: "bg-blue-100 text-blue-800",
    };

    const sizes = {
      sm: dot ? "w-2 h-2" : "px-2 py-0.5 text-xs",
      md: dot ? "w-2.5 h-2.5" : "px-2.5 py-0.5 text-sm",
      lg: dot ? "w-3 h-3" : "px-3 py-1 text-sm",
    };

    if (dot) {
      return (
        <span
          className={cn(
            "rounded-full",
            variants[variant],
            sizes[size],
            className
          )}
          ref={ref}
          {...props}
        />
      );
    }

    return (
      <span
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";

export { Badge };
