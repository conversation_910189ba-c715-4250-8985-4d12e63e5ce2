"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Button,
  Badge,
  <PERSON>,
  Loading<PERSON>pin<PERSON>,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import ProductGrid from "./components/ProductGrid";
import ShoppingCart from "./components/ShoppingCart";
import PaymentModal from "./components/PaymentModal";
import CustomerModal from "./components/CustomerModal";

interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  category: string;
  barcode?: string;
  image?: string;
}

interface CartItem {
  productId: string;
  productName: string;
  price: number;
  quantity: number;
  subtotal: number;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  discount?: number;
}

const POSPage: React.FC = () => {
  const { user, checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("ALL");
  
  // Modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);

  const canProcessSales = checkPermission(PERMISSIONS.ORDER_CREATE);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock products data optimized for POS
      setProducts([
        {
          id: "1",
          name: "Indomie Goreng",
          price: 3000,
          stock: 500,
          category: "Makanan Instan",
          barcode: "8992388101015",
          image: "/api/placeholder/150/150",
        },
        {
          id: "2",
          name: "Teh Botol Sosro",
          price: 4500,
          stock: 200,
          category: "Minuman",
          barcode: "8992761111014",
          image: "/api/placeholder/150/150",
        },
        {
          id: "3",
          name: "Chitato Sapi Panggang",
          price: 8500,
          stock: 150,
          category: "Makanan Ringan",
          barcode: "8992760221015",
          image: "/api/placeholder/150/150",
        },
        {
          id: "4",
          name: "Royco Kaldu Ayam",
          price: 2500,
          stock: 300,
          category: "Bumbu Dapur",
          barcode: "8992760331016",
          image: "/api/placeholder/150/150",
        },
        {
          id: "5",
          name: "Beras Premium 5kg",
          price: 75000,
          stock: 100,
          category: "Bahan Pokok",
          barcode: "8992760441017",
          image: "/api/placeholder/150/150",
        },
        {
          id: "6",
          name: "Minyak Goreng 1L",
          price: 18000,
          stock: 80,
          category: "Bahan Pokok",
          barcode: "8992760551018",
          image: "/api/placeholder/150/150",
        },
      ]);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product: Product, quantity: number = 1) => {
    if (product.stock < quantity) {
      toast.error("Insufficient stock");
      return;
    }

    setCart(prev => {
      const existingItem = prev.find(item => item.productId === product.id);
      
      if (existingItem) {
        const newQuantity = existingItem.quantity + quantity;
        if (newQuantity > product.stock) {
          toast.error("Insufficient stock");
          return prev;
        }
        
        return prev.map(item =>
          item.productId === product.id
            ? {
                ...item,
                quantity: newQuantity,
                subtotal: newQuantity * item.price,
              }
            : item
        );
      } else {
        return [...prev, {
          productId: product.id,
          productName: product.name,
          price: product.price,
          quantity,
          subtotal: product.price * quantity,
        }];
      }
    });

    toast.success(`${product.name} added to cart`);
  };

  const handleUpdateCartItem = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveFromCart(productId);
      return;
    }

    const product = products.find(p => p.id === productId);
    if (product && quantity > product.stock) {
      toast.error("Insufficient stock");
      return;
    }

    setCart(prev =>
      prev.map(item =>
        item.productId === productId
          ? {
              ...item,
              quantity,
              subtotal: quantity * item.price,
            }
          : item
      )
    );
  };

  const handleRemoveFromCart = (productId: string) => {
    setCart(prev => prev.filter(item => item.productId !== productId));
  };

  const handleClearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
  };

  const handleBarcodeSearch = (barcode: string) => {
    const product = products.find(p => p.barcode === barcode);
    if (product) {
      handleAddToCart(product);
      setSearchQuery("");
    } else {
      toast.error("Product not found");
    }
  };

  const handleProcessPayment = async (paymentData: any) => {
    try {
      // Simulate payment processing - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create order
      const orderData = {
        items: cart,
        customer: selectedCustomer,
        payment: paymentData,
        total: calculateTotal(),
        cashier: user?.name,
        timestamp: new Date().toISOString(),
      };

      console.log("Processing order:", orderData);
      
      // Clear cart and reset
      handleClearCart();
      setShowPaymentModal(false);
      
      toast.success("Payment processed successfully!");
    } catch (error) {
      console.error("Error processing payment:", error);
      toast.error("Payment processing failed");
    }
  };

  const calculateSubtotal = () => {
    return cart.reduce((sum, item) => sum + item.subtotal, 0);
  };

  const calculateDiscount = () => {
    if (!selectedCustomer?.discount) return 0;
    return (calculateSubtotal() * selectedCustomer.discount) / 100;
  };

  const calculateTax = () => {
    const subtotal = calculateSubtotal() - calculateDiscount();
    return subtotal * 0.1; // 10% tax
  };

  const calculateTotal = () => {
    return calculateSubtotal() - calculateDiscount() + calculateTax();
  };

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.barcode?.includes(searchQuery);
    const matchesCategory = selectedCategory === "ALL" || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = ["ALL", ...Array.from(new Set(products.map(p => p.category)))];

  if (!canProcessSales) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-secondary-900 mb-2">
            Access Denied
          </h2>
          <p className="text-secondary-600">
            You don't have permission to access the POS system.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-secondary-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-secondary-900">Point of Sale</h1>
            <p className="text-secondary-600">Cashier: {user?.name}</p>
          </div>
          <div className="flex items-center space-x-4">
            <Badge variant="success" className="px-3 py-1">
              Online
            </Badge>
            <Button
              variant="outline"
              onClick={() => setShowCustomerModal(true)}
            >
              {selectedCustomer ? selectedCustomer.name : "Select Customer"}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Products */}
        <div className="flex-1 flex flex-col">
          {/* Search and Categories */}
          <div className="bg-white p-4 border-b">
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex-1">
                <SearchInput
                  placeholder="Search products or scan barcode..."
                  value={searchQuery}
                  onSearch={(query) => {
                    setSearchQuery(query);
                    // Check if it's a barcode (numeric and specific length)
                    if (/^\d{13}$/.test(query)) {
                      handleBarcodeSearch(query);
                    }
                  }}
                  className="text-lg"
                />
              </div>
            </div>
            
            {/* Category Buttons */}
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "primary" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1 overflow-auto p-4">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <LoadingSpinner size="lg" text="Loading products..." />
              </div>
            ) : (
              <ProductGrid
                products={filteredProducts}
                onAddToCart={handleAddToCart}
              />
            )}
          </div>
        </div>

        {/* Right Panel - Cart */}
        <div className="w-96 bg-white border-l flex flex-col">
          <ShoppingCart
            items={cart}
            customer={selectedCustomer}
            onUpdateItem={handleUpdateCartItem}
            onRemoveItem={handleRemoveFromCart}
            onClearCart={handleClearCart}
            subtotal={calculateSubtotal()}
            discount={calculateDiscount()}
            tax={calculateTax()}
            total={calculateTotal()}
            onCheckout={() => setShowPaymentModal(true)}
            disabled={cart.length === 0}
          />
        </div>
      </div>

      {/* Payment Modal */}
      <Modal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        title="Process Payment"
        size="lg"
      >
        <PaymentModal
          total={calculateTotal()}
          onProcessPayment={handleProcessPayment}
          onCancel={() => setShowPaymentModal(false)}
        />
      </Modal>

      {/* Customer Modal */}
      <Modal
        isOpen={showCustomerModal}
        onClose={() => setShowCustomerModal(false)}
        title="Select Customer"
        size="md"
      >
        <CustomerModal
          selectedCustomer={selectedCustomer}
          onSelectCustomer={(customer) => {
            setSelectedCustomer(customer);
            setShowCustomerModal(false);
          }}
          onCancel={() => setShowCustomerModal(false)}
        />
      </Modal>
    </div>
  );
};

export default POSPage;
