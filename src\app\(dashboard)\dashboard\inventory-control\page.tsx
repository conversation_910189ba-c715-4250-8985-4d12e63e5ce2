"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Stats,
  Button,
  Select,
  LoadingSpinner,
  Badge,
  Modal,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import InventoryOverview from "./components/InventoryOverview";
import StockAlerts from "./components/StockAlerts";
import InventoryMovements from "./components/InventoryMovements";
import InventoryReports from "./components/InventoryReports";
import StockAdjustmentModal from "./components/StockAdjustmentModal";

interface InventoryStats {
  totalProducts: number;
  totalValue: number;
  lowStockItems: number;
  outOfStockItems: number;
  reorderRequired: number;
  averageTurnover: number;
  monthlyMovements: number;
  warehouseCount: number;
}

interface StockAlert {
  id: string;
  productId: string;
  productName: string;
  currentStock: number;
  minStock: number;
  category: string;
  warehouse: string;
  alertType: "LOW_STOCK" | "OUT_OF_STOCK" | "OVERSTOCK" | "EXPIRING";
  severity: "HIGH" | "MEDIUM" | "LOW";
  createdAt: string;
}

const InventoryControlPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState("30");
  const [selectedWarehouse, setSelectedWarehouse] = useState("ALL");
  const [activeTab, setActiveTab] = useState("overview");
  
  // Modal states
  const [showAdjustmentModal, setShowAdjustmentModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  const canUpdate = checkPermission(PERMISSIONS.INVENTORY_UPDATE);
  const canViewReports = checkPermission(PERMISSIONS.REPORTS_READ);

  useEffect(() => {
    fetchInventoryData();
  }, [selectedPeriod, selectedWarehouse]);

  const fetchInventoryData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock stats data
      setStats({
        totalProducts: 156,
        totalValue: 245680000,
        lowStockItems: 23,
        outOfStockItems: 8,
        reorderRequired: 31,
        averageTurnover: 4.2,
        monthlyMovements: 1247,
        warehouseCount: 3,
      });

      // Mock alerts data
      setAlerts([
        {
          id: "1",
          productId: "P001",
          productName: "Indomie Goreng Original",
          currentStock: 5,
          minStock: 50,
          category: "Makanan Instan",
          warehouse: "Gudang Utama",
          alertType: "LOW_STOCK",
          severity: "HIGH",
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "2",
          productId: "P002",
          productName: "Teh Botol Sosro 450ml",
          currentStock: 0,
          minStock: 20,
          category: "Minuman",
          warehouse: "Gudang Utama",
          alertType: "OUT_OF_STOCK",
          severity: "HIGH",
          createdAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "3",
          productId: "P003",
          productName: "Chitato Sapi Panggang",
          currentStock: 8,
          minStock: 25,
          category: "Makanan Ringan",
          warehouse: "Gudang Cabang",
          alertType: "LOW_STOCK",
          severity: "MEDIUM",
          createdAt: "2024-01-14T16:45:00Z",
        },
        {
          id: "4",
          productId: "P004",
          productName: "Minyak Goreng Tropical",
          currentStock: 150,
          minStock: 30,
          category: "Bahan Pokok",
          warehouse: "Gudang Utama",
          alertType: "OVERSTOCK",
          severity: "LOW",
          createdAt: "2024-01-14T14:20:00Z",
        },
      ]);
    } catch (error) {
      console.error("Error fetching inventory data:", error);
      toast.error("Failed to fetch inventory data");
    } finally {
      setLoading(false);
    }
  };

  const handleStockAdjustment = (product: any) => {
    setSelectedProduct(product);
    setShowAdjustmentModal(true);
  };

  const handleAdjustmentSave = async (adjustmentData: any) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success("Stock adjustment recorded successfully");
      setShowAdjustmentModal(false);
      setSelectedProduct(null);
      
      // Refresh data
      fetchInventoryData();
    } catch (error) {
      console.error("Error recording stock adjustment:", error);
      toast.error("Failed to record stock adjustment");
    }
  };

  const periodOptions = [
    { value: "7", label: "Last 7 days" },
    { value: "30", label: "Last 30 days" },
    { value: "90", label: "Last 3 months" },
    { value: "365", label: "Last year" },
  ];

  const warehouseOptions = [
    { value: "ALL", label: "All Warehouses" },
    { value: "1", label: "Gudang Utama" },
    { value: "2", label: "Gudang Cabang" },
    { value: "3", label: "Gudang Frozen" },
  ];

  const tabs = [
    { id: "overview", label: "Overview", icon: "📊" },
    { id: "alerts", label: "Stock Alerts", icon: "⚠️", count: alerts.length },
    { id: "movements", label: "Movements", icon: "📦" },
    { id: "reports", label: "Reports", icon: "📈" },
  ];

  const statsData = stats ? [
    {
      label: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      change: {
        value: 5.2,
        type: "increase" as const,
        period: "vs last month",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Total Value",
      value: formatCurrency(stats.totalValue),
      change: {
        value: 12.8,
        type: "increase" as const,
        period: "vs last month",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: "success" as const,
    },
    {
      label: "Low Stock Items",
      value: stats.lowStockItems.toLocaleString(),
      change: {
        value: 8.3,
        type: "increase" as const,
        period: "vs last week",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      color: "warning" as const,
    },
    {
      label: "Out of Stock",
      value: stats.outOfStockItems.toLocaleString(),
      change: {
        value: 15.7,
        type: "decrease" as const,
        period: "vs last week",
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      ),
      color: "error" as const,
    },
  ] : [];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading inventory data..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Inventory Control</h1>
          <p className="text-secondary-600">
            Real-time inventory tracking and management
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {canUpdate && (
            <Button onClick={() => setShowAdjustmentModal(true)}>
              Stock Adjustment
            </Button>
          )}
          <Button variant="outline">
            Export Report
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-secondary-700">Period:</label>
              <Select
                options={periodOptions}
                value={selectedPeriod}
                onChange={setSelectedPeriod}
                className="w-40"
              />
            </div>
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-secondary-700">Warehouse:</label>
              <Select
                options={warehouseOptions}
                value={selectedWarehouse}
                onChange={setSelectedWarehouse}
                className="w-48"
              />
            </div>
            <Button variant="outline" onClick={fetchInventoryData}>
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Stats stats={statsData} columns={4} />

      {/* Navigation Tabs */}
      <div className="border-b border-secondary-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === tab.id
                  ? "border-primary-500 text-primary-600"
                  : "border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300"
              }`}
            >
              <div className="flex items-center space-x-2">
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
                {tab.count && (
                  <Badge variant="error" size="sm">
                    {tab.count}
                  </Badge>
                )}
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === "overview" && (
          <InventoryOverview
            stats={stats}
            period={selectedPeriod}
            warehouse={selectedWarehouse}
            onStockAdjustment={handleStockAdjustment}
          />
        )}
        
        {activeTab === "alerts" && (
          <StockAlerts
            alerts={alerts}
            onResolveAlert={(alertId) => {
              setAlerts(prev => prev.filter(a => a.id !== alertId));
              toast.success("Alert resolved");
            }}
            onStockAdjustment={handleStockAdjustment}
          />
        )}
        
        {activeTab === "movements" && (
          <InventoryMovements
            period={selectedPeriod}
            warehouse={selectedWarehouse}
          />
        )}
        
        {activeTab === "reports" && canViewReports && (
          <InventoryReports
            period={selectedPeriod}
            warehouse={selectedWarehouse}
          />
        )}
      </div>

      {/* Stock Adjustment Modal */}
      <Modal
        isOpen={showAdjustmentModal}
        onClose={() => {
          setShowAdjustmentModal(false);
          setSelectedProduct(null);
        }}
        title="Stock Adjustment"
        size="md"
      >
        <StockAdjustmentModal
          product={selectedProduct}
          onSave={handleAdjustmentSave}
          onCancel={() => {
            setShowAdjustmentModal(false);
            setSelectedProduct(null);
          }}
        />
      </Modal>
    </div>
  );
};

export default InventoryControlPage;
