import { NextRequest } from "next/server";
import { distributorAgent } from "@/ai/agent";
import { PERMISSIONS } from "@/config/constants";
import {
  withAuth,
  successResponse,
  errorResponse,
  getQueryParams,
  logApiRequest,
} from "@/lib/api-utils";

// GET /api/ai/search - AI-powered product search
export const GET = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "AI_SEARCH");

    const { query } = getQueryParams(req);

    if (!query || query.trim().length === 0) {
      return errorResponse("Search query is required", 400);
    }

    try {
      const searchResults = await distributorAgent.searchProducts(query, user.id);
      
      return successResponse(searchResults, "Search completed successfully");
    } catch (error: any) {
      console.error("AI search error:", error);
      return errorResponse("Search failed: " + error.message, 500);
    }
  },
  [PERMISSIONS.PRODUCT_READ]
);

// POST /api/ai/search - Advanced AI search with filters
export const POST = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "AI_SEARCH_ADVANCED");

    try {
      const body = await req.json();
      const { query, filters, limit } = body;

      if (!query || query.trim().length === 0) {
        return errorResponse("Search query is required", 400);
      }

      // Enhance query with filters
      let enhancedQuery = query;
      if (filters) {
        if (filters.category) {
          enhancedQuery += ` in category ${filters.category}`;
        }
        if (filters.warehouse) {
          enhancedQuery += ` from warehouse ${filters.warehouse}`;
        }
        if (filters.priceRange) {
          enhancedQuery += ` with price between ${filters.priceRange.min} and ${filters.priceRange.max}`;
        }
        if (filters.inStock) {
          enhancedQuery += ` that are in stock`;
        }
      }

      const searchResults = await distributorAgent.searchProducts(enhancedQuery, user.id);

      // Apply additional filters if needed
      let filteredProducts = searchResults.products;
      
      if (filters?.priceRange) {
        filteredProducts = filteredProducts.filter(
          product => 
            product.price >= filters.priceRange.min && 
            product.price <= filters.priceRange.max
        );
      }

      if (filters?.inStock) {
        filteredProducts = filteredProducts.filter(product => product.stock > 0);
      }

      if (limit && limit > 0) {
        filteredProducts = filteredProducts.slice(0, limit);
      }

      const finalResults = {
        ...searchResults,
        products: filteredProducts,
        totalFound: filteredProducts.length,
        filters: filters || {},
      };

      return successResponse(finalResults, "Advanced search completed successfully");
    } catch (error: any) {
      console.error("Advanced AI search error:", error);
      return errorResponse("Advanced search failed: " + error.message, 500);
    }
  },
  [PERMISSIONS.PRODUCT_READ]
);
