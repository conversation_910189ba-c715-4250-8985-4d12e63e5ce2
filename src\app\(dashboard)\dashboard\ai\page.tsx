"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardContent,
  Button,
  Input,
  Badge,
  LoadingSpinner,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { useAuth } from "@/hooks/useAuth";
import { formatCurrency } from "@/lib/utils";

interface ChatMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: string;
}

interface SearchResult {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: string;
  warehouse: string;
}

const AIAssistantPage: React.FC = () => {
  const { user } = useAuth();
  const toast = useToastActions();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);

  useEffect(() => {
    // Add welcome message
    setMessages([
      {
        id: "welcome",
        type: "assistant",
        content: `Hello ${user?.name}! I'm your AI assistant for Arta Boga. I can help you with:\n\n• Product search and recommendations\n• Inventory management insights\n• Customer analysis\n• Order processing assistance\n• Business analytics\n\nHow can I help you today?`,
        timestamp: new Date().toISOString(),
      },
    ]);
  }, [user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: "user",
      content: inputMessage,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    try {
      // Simulate AI response - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: generateAIResponse(inputMessage),
        timestamp: new Date().toISOString(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
    } finally {
      setIsTyping(false);
    }
  };

  const generateAIResponse = (input: string): string => {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes("product") || lowerInput.includes("search")) {
      return "I can help you search for products! Try using the product search feature on the right, or tell me what specific product you're looking for. I can also provide recommendations based on sales data and inventory levels.";
    }
    
    if (lowerInput.includes("inventory") || lowerInput.includes("stock")) {
      return "For inventory management, I can help you:\n\n• Check current stock levels\n• Identify low-stock items\n• Suggest reorder quantities\n• Analyze inventory turnover\n\nWould you like me to check your current inventory status?";
    }
    
    if (lowerInput.includes("customer") || lowerInput.includes("sales")) {
      return "I can analyze customer data and sales patterns:\n\n• Top customers by revenue\n• Customer purchase behavior\n• Sales trends and forecasts\n• Customer segmentation insights\n\nWhat specific customer analysis would you like to see?";
    }
    
    if (lowerInput.includes("report") || lowerInput.includes("analytics")) {
      return "I can generate various reports and analytics:\n\n• Sales performance reports\n• Inventory analysis\n• Customer insights\n• Financial summaries\n• Trend analysis\n\nWhich type of report would you like me to prepare?";
    }
    
    return "I understand you're asking about: \"" + input + "\"\n\nI can help you with product management, inventory tracking, customer analysis, and business insights. Could you please be more specific about what you'd like assistance with?";
  };

  const handleProductSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setSearchLoading(true);
      
      // Simulate AI-powered search - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock search results
      const mockResults: SearchResult[] = [
        {
          id: "1",
          name: "Chitato Rasa Sapi Panggang",
          description: "Keripik kentang rasa sapi panggang 68g",
          price: 8500,
          stock: 100,
          category: "Makanan Ringan",
          warehouse: "Gudang Utama",
        },
        {
          id: "2",
          name: "Teh Botol Sosro",
          description: "Minuman teh dalam kemasan botol 450ml",
          price: 4500,
          stock: 8,
          category: "Minuman",
          warehouse: "Gudang Utama",
        },
      ].filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase())
      );

      setSearchResults(mockResults);
      
      // Add search result to chat
      if (mockResults.length > 0) {
        const searchMessage: ChatMessage = {
          id: Date.now().toString(),
          type: "assistant",
          content: `I found ${mockResults.length} product(s) matching "${query}". Check the search results on the right for details. Would you like me to provide more information about any of these products?`,
          timestamp: new Date().toISOString(),
        };
        setMessages(prev => [...prev, searchMessage]);
      } else {
        const noResultsMessage: ChatMessage = {
          id: Date.now().toString(),
          type: "assistant",
          content: `I couldn't find any products matching "${query}". Would you like me to suggest similar products or help you add a new product to your inventory?`,
          timestamp: new Date().toISOString(),
        };
        setMessages(prev => [...prev, noResultsMessage]);
      }
    } catch (error) {
      console.error("Error searching products:", error);
      toast.error("Failed to search products");
    } finally {
      setSearchLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="error">Out of Stock</Badge>;
    } else if (stock <= 10) {
      return <Badge variant="warning">Low Stock</Badge>;
    } else {
      return <Badge variant="success">In Stock</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">AI Assistant</h1>
        <p className="text-secondary-600">
          Get intelligent insights and assistance for your business operations
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Interface */}
        <div className="lg:col-span-2">
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle>Chat with AI Assistant</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-2 ${
                        message.type === "user"
                          ? "bg-primary-600 text-white"
                          : "bg-secondary-100 text-secondary-900"
                      }`}
                    >
                      <div className="whitespace-pre-wrap">{message.content}</div>
                      <div
                        className={`text-xs mt-1 ${
                          message.type === "user" ? "text-primary-100" : "text-secondary-500"
                        }`}
                      >
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-secondary-100 rounded-lg px-4 py-2">
                      <div className="flex items-center space-x-1">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                        </div>
                        <span className="text-sm text-secondary-600 ml-2">AI is typing...</span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Input */}
              <div className="flex space-x-2">
                <Input
                  placeholder="Type your message..."
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isTyping}
                >
                  Send
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Product Search */}
        <div>
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle>AI Product Search</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <div className="mb-4">
                <SearchInput
                  placeholder="Search products with AI..."
                  value={searchQuery}
                  onSearch={handleProductSearch}
                  loading={searchLoading}
                />
              </div>

              <div className="flex-1 overflow-y-auto">
                {searchLoading ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner size="md" text="Searching..." />
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="space-y-3">
                    {searchResults.map((product) => (
                      <div
                        key={product.id}
                        className="p-3 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-secondary-900 text-sm">
                            {product.name}
                          </h4>
                          {getStockBadge(product.stock)}
                        </div>
                        
                        {product.description && (
                          <p className="text-xs text-secondary-600 mb-2">
                            {product.description}
                          </p>
                        )}
                        
                        <div className="flex justify-between items-center text-xs">
                          <span className="font-medium text-primary-600">
                            {formatCurrency(product.price)}
                          </span>
                          <span className="text-secondary-500">
                            {product.category}
                          </span>
                        </div>
                        
                        <div className="mt-2 text-xs text-secondary-500">
                          Stock: {product.stock} • {product.warehouse}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : searchQuery ? (
                  <div className="text-center py-8 text-secondary-500">
                    No products found for "{searchQuery}"
                  </div>
                ) : (
                  <div className="text-center py-8 text-secondary-500">
                    Use AI-powered search to find products quickly
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AIAssistantPage;
