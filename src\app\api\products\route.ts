import { NextRequest } from "next/server";
import prisma from "@/db/client";
import { productSchema } from "@/lib/validations";
import { PERMISSIONS } from "@/config/constants";
import {
  withAuth,
  successResponse,
  paginatedResponse,
  validateRequest,
  getQueryParams,
  buildSearchQuery,
  buildSortQuery,
  logApiRequest,
} from "@/lib/api-utils";

// GET /api/products - Get all products with pagination and search
export const GET = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "GET_PRODUCTS");

    const { page, limit, query, sortBy, sortOrder, filters } = getQueryParams(req);

    // Build search conditions
    const searchConditions = buildSearchQuery(query, ["name", "description"]);
    
    // Build filter conditions
    const filterConditions: any = {};
    if (filters.categoryId) {
      filterConditions.categoryId = filters.categoryId;
    }
    if (filters.warehouseId) {
      filterConditions.warehouseId = filters.warehouseId;
    }
    if (filters.minPrice) {
      filterConditions.price = { ...filterConditions.price, gte: parseFloat(filters.minPrice) };
    }
    if (filters.maxPrice) {
      filterConditions.price = { ...filterConditions.price, lte: parseFloat(filters.maxPrice) };
    }
    if (filters.inStock === "true") {
      filterConditions.stock = { gt: 0 };
    }
    if (filters.lowStock === "true") {
      filterConditions.stock = { lte: 10 };
    }

    const whereConditions = {
      ...searchConditions,
      ...filterConditions,
    };

    // Get total count
    const total = await prisma.product.count({
      where: whereConditions,
    });

    // Get products
    const products = await prisma.product.findMany({
      where: whereConditions,
      include: {
        category: true,
        warehouse: true,
        _count: {
          select: {
            orderItems: true,
          },
        },
      },
      orderBy: buildSortQuery(sortBy, sortOrder),
      skip: (page - 1) * limit,
      take: limit,
    });

    return paginatedResponse(products, total, page, limit);
  },
  [PERMISSIONS.PRODUCT_READ]
);

// POST /api/products - Create new product
export const POST = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "CREATE_PRODUCT");

    const validatedData = await validateRequest(req, productSchema);

    // Check if category and warehouse exist
    const [category, warehouse] = await Promise.all([
      prisma.category.findUnique({ where: { id: validatedData.categoryId } }),
      prisma.warehouse.findUnique({ where: { id: validatedData.warehouseId } }),
    ]);

    if (!category) {
      throw new Error("Category not found");
    }
    if (!warehouse) {
      throw new Error("Warehouse not found");
    }

    // Create product
    const product = await prisma.product.create({
      data: validatedData,
      include: {
        category: true,
        warehouse: true,
      },
    });

    // Create inventory transaction
    await prisma.inventoryTransaction.create({
      data: {
        productId: product.id,
        type: "PURCHASE",
        quantity: product.stock,
        reason: "Initial stock",
        userId: user.id,
      },
    });

    // Create notification
    await prisma.notification.create({
      data: {
        title: "New Product Added",
        message: `Product "${product.name}" has been added to inventory`,
        type: "INFO",
        priority: "LOW",
        metadata: {
          productId: product.id,
          productName: product.name,
          addedBy: user.name,
        },
      },
    });

    return successResponse(product, "Product created successfully");
  },
  [PERMISSIONS.PRODUCT_CREATE]
);

// PUT /api/products - Bulk update products
export const PUT = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "BULK_UPDATE_PRODUCTS");

    const { productIds, updates } = await req.json();

    if (!Array.isArray(productIds) || productIds.length === 0) {
      throw new Error("Product IDs array is required");
    }

    // Validate updates
    const allowedUpdates = ["price", "stock", "categoryId", "warehouseId"];
    const validUpdates: any = {};
    
    for (const [key, value] of Object.entries(updates)) {
      if (allowedUpdates.includes(key)) {
        validUpdates[key] = value;
      }
    }

    if (Object.keys(validUpdates).length === 0) {
      throw new Error("No valid updates provided");
    }

    // Update products
    const updatedProducts = await prisma.$transaction(
      productIds.map((id: string) =>
        prisma.product.update({
          where: { id },
          data: validUpdates,
          include: {
            category: true,
            warehouse: true,
          },
        })
      )
    );

    // Create notification
    await prisma.notification.create({
      data: {
        title: "Bulk Product Update",
        message: `${productIds.length} products have been updated`,
        type: "INFO",
        priority: "LOW",
        metadata: {
          productCount: productIds.length,
          updates: validUpdates,
          updatedBy: user.name,
        },
      },
    });

    return successResponse(updatedProducts, "Products updated successfully");
  },
  [PERMISSIONS.PRODUCT_UPDATE]
);

// DELETE /api/products - Bulk delete products
export const DELETE = withAuth(
  async (req: NextRequest, context: any, user: any) => {
    logApiRequest(req, user, "BULK_DELETE_PRODUCTS");

    const { productIds } = await req.json();

    if (!Array.isArray(productIds) || productIds.length === 0) {
      throw new Error("Product IDs array is required");
    }

    // Check if products have orders
    const productsWithOrders = await prisma.product.findMany({
      where: {
        id: { in: productIds },
        orderItems: {
          some: {},
        },
      },
      select: { id: true, name: true },
    });

    if (productsWithOrders.length > 0) {
      throw new Error(
        `Cannot delete products with existing orders: ${productsWithOrders
          .map((p) => p.name)
          .join(", ")}`
      );
    }

    // Delete products
    const deletedCount = await prisma.product.deleteMany({
      where: { id: { in: productIds } },
    });

    // Create notification
    await prisma.notification.create({
      data: {
        title: "Products Deleted",
        message: `${deletedCount.count} products have been deleted`,
        type: "WARNING",
        priority: "MEDIUM",
        metadata: {
          deletedCount: deletedCount.count,
          deletedBy: user.name,
        },
      },
    });

    return successResponse(
      { deletedCount: deletedCount.count },
      "Products deleted successfully"
    );
  },
  [PERMISSIONS.PRODUCT_DELETE]
);
