"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
  CardContent,
  Table,
  <PERSON>ton,
  Badge,
  Modal,
  LoadingSpinner,
  EmptyState,
  Pagination,
  Select,
  Stats,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import InventoryAdjustmentModal from "./components/InventoryAdjustmentModal";

interface InventoryTransaction {
  id: string;
  productName: string;
  type: "PURCHASE" | "SALE" | "ADJUSTMENT" | "RETURN" | "TRANSFER";
  quantity: number;
  reason: string;
  userName: string;
  createdAt: string;
}

interface InventoryStats {
  totalProducts: number;
  lowStockItems: number;
  outOfStockItems: number;
  totalTransactions: number;
}

const InventoryPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Modal states
  const [showAdjustmentModal, setShowAdjustmentModal] = useState(false);

  const canUpdate = checkPermission(PERMISSIONS.INVENTORY_UPDATE);

  useEffect(() => {
    fetchInventoryData();
  }, [currentPage, itemsPerPage, searchQuery, typeFilter]);

  const fetchInventoryData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock stats data
      setStats({
        totalProducts: 89,
        lowStockItems: 12,
        outOfStockItems: 3,
        totalTransactions: 1247,
      });

      // Mock transactions data
      const mockTransactions: InventoryTransaction[] = [
        {
          id: "TXN-001",
          productName: "Indomie Goreng",
          type: "SALE",
          quantity: -100,
          reason: "Order ORD-001",
          userName: "John Doe",
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "TXN-002",
          productName: "Teh Botol Sosro",
          type: "PURCHASE",
          quantity: 200,
          reason: "Stock replenishment",
          userName: "Jane Smith",
          createdAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "TXN-003",
          productName: "Chitato Sapi Panggang",
          type: "ADJUSTMENT",
          quantity: -5,
          reason: "Damaged goods",
          userName: "Admin",
          createdAt: "2024-01-14T16:45:00Z",
        },
        {
          id: "TXN-004",
          productName: "Royco Kaldu Ayam",
          type: "RETURN",
          quantity: 25,
          reason: "Customer return",
          userName: "Customer Service",
          createdAt: "2024-01-14T14:20:00Z",
        },
        {
          id: "TXN-005",
          productName: "Beras Premium",
          type: "TRANSFER",
          quantity: -50,
          reason: "Transfer to Branch Warehouse",
          userName: "Warehouse Manager",
          createdAt: "2024-01-13T11:30:00Z",
        },
      ];

      // Apply filters
      let filteredTransactions = mockTransactions;
      
      if (searchQuery) {
        filteredTransactions = filteredTransactions.filter(transaction =>
          transaction.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          transaction.reason.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      
      if (typeFilter !== "ALL") {
        filteredTransactions = filteredTransactions.filter(transaction => transaction.type === typeFilter);
      }

      setTransactions(filteredTransactions);
      setTotalItems(filteredTransactions.length);
      setTotalPages(Math.ceil(filteredTransactions.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching inventory data:", error);
      toast.error("Failed to fetch inventory data");
    } finally {
      setLoading(false);
    }
  };

  const handleInventoryAdjustment = async (adjustmentData: any) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newTransaction: InventoryTransaction = {
        id: `TXN-${String(Date.now()).slice(-3)}`,
        productName: adjustmentData.productName,
        type: "ADJUSTMENT",
        quantity: adjustmentData.quantity,
        reason: adjustmentData.reason,
        userName: "Current User", // Replace with actual user name
        createdAt: new Date().toISOString(),
      };
      
      setTransactions(prev => [newTransaction, ...prev]);
      setShowAdjustmentModal(false);
      toast.success("Inventory adjustment recorded successfully");
      
      // Refresh stats
      fetchInventoryData();
    } catch (error) {
      console.error("Error recording inventory adjustment:", error);
      toast.error("Failed to record inventory adjustment");
    }
  };

  const getTransactionBadge = (type: string, quantity: number) => {
    const config = {
      PURCHASE: { variant: "success" as const, label: "Purchase" },
      SALE: { variant: "primary" as const, label: "Sale" },
      ADJUSTMENT: { variant: "warning" as const, label: "Adjustment" },
      RETURN: { variant: "info" as const, label: "Return" },
      TRANSFER: { variant: "secondary" as const, label: "Transfer" },
    };

    const typeConfig = config[type as keyof typeof config] || {
      variant: "default" as const,
      label: type,
    };

    return <Badge variant={typeConfig.variant}>{typeConfig.label}</Badge>;
  };

  const typeOptions = [
    { value: "ALL", label: "All Types" },
    { value: "PURCHASE", label: "Purchase" },
    { value: "SALE", label: "Sale" },
    { value: "ADJUSTMENT", label: "Adjustment" },
    { value: "RETURN", label: "Return" },
    { value: "TRANSFER", label: "Transfer" },
  ];

  const columns = [
    {
      key: "id",
      title: "Transaction ID",
      dataIndex: "id",
      sortable: true,
    },
    {
      key: "productName",
      title: "Product",
      dataIndex: "productName",
      sortable: true,
    },
    {
      key: "type",
      title: "Type",
      dataIndex: "type",
      render: (value: string, record: InventoryTransaction) => 
        getTransactionBadge(value, record.quantity),
    },
    {
      key: "quantity",
      title: "Quantity",
      dataIndex: "quantity",
      render: (value: number) => (
        <span className={value > 0 ? "text-success-600" : "text-error-600"}>
          {value > 0 ? "+" : ""}{value.toLocaleString()}
        </span>
      ),
      sortable: true,
    },
    {
      key: "reason",
      title: "Reason",
      dataIndex: "reason",
    },
    {
      key: "userName",
      title: "User",
      dataIndex: "userName",
    },
    {
      key: "createdAt",
      title: "Date",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
      sortable: true,
    },
  ];

  const statsData = stats ? [
    {
      label: "Total Products",
      value: stats.totalProducts.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Low Stock Items",
      value: stats.lowStockItems.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      color: "warning" as const,
    },
    {
      label: "Out of Stock",
      value: stats.outOfStockItems.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      ),
      color: "error" as const,
    },
    {
      label: "Total Transactions",
      value: stats.totalTransactions.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      color: "info" as const,
    },
  ] : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Inventory Management</h1>
          <p className="text-secondary-600">Track inventory movements and stock levels</p>
        </div>
        {canUpdate && (
          <Button onClick={() => setShowAdjustmentModal(true)}>
            Record Adjustment
          </Button>
        )}
      </div>

      {/* Statistics */}
      <Stats stats={statsData} columns={4} />

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search transactions..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Select
              options={typeOptions}
              value={typeFilter}
              onChange={setTypeFilter}
              className="w-40"
            />
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Transactions ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading transactions..." />
            </div>
          ) : transactions.length === 0 ? (
            <EmptyState
              title="No transactions found"
              description="No inventory transactions match your current filters"
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={transactions}
                loading={loading}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Inventory Adjustment Modal */}
      <Modal
        isOpen={showAdjustmentModal}
        onClose={() => setShowAdjustmentModal(false)}
        title="Record Inventory Adjustment"
        size="md"
      >
        <InventoryAdjustmentModal
          onSave={handleInventoryAdjustment}
          onCancel={() => setShowAdjustmentModal(false)}
        />
      </Modal>
    </div>
  );
};

export default InventoryPage;
