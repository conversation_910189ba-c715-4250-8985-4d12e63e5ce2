"use client";

import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  <PERSON>ton,
  Badge,
  LoadingSpinner,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency } from "@/lib/utils";

interface SearchSuggestion {
  id: string;
  type: "product" | "category" | "brand" | "query";
  text: string;
  description?: string;
  count?: number;
}

interface AISearchResult {
  id: string;
  name: string;
  price: number;
  category: string;
  relevanceScore: number;
  matchReason: string;
}

interface AISearchBarProps {
  onSearch: (query: string) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  className?: string;
}

const AISearchBar: React.FC<AISearchBarProps> = ({
  onSearch,
  onSuggestionSelect,
  className,
}) => {
  const toast = useToastActions();
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [aiResults, setAiResults] = useState<AISearchResult[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (query.length > 0) {
      fetchSuggestions(query);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [query]);

  const fetchSuggestions = async (searchQuery: string) => {
    try {
      setLoading(true);
      
      // Simulate API call for suggestions - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Mock suggestions data
      const mockSuggestions: SearchSuggestion[] = [
        {
          id: "1",
          type: "product",
          text: "Indomie Goreng",
          description: "Instant noodles",
          count: 15,
        },
        {
          id: "2",
          type: "category",
          text: "Makanan Instan",
          description: "Instant food category",
          count: 45,
        },
        {
          id: "3",
          type: "brand",
          text: "Indofood",
          description: "Food brand",
          count: 23,
        },
        {
          id: "4",
          type: "query",
          text: `"${searchQuery}" in snacks`,
          description: "Search in snacks category",
        },
        {
          id: "5",
          type: "query",
          text: `"${searchQuery}" under 10k`,
          description: "Search products under 10,000",
        },
      ].filter(suggestion => 
        suggestion.text.toLowerCase().includes(searchQuery.toLowerCase())
      );

      setSuggestions(mockSuggestions);
      setShowSuggestions(true);
    } catch (error) {
      console.error("Error fetching suggestions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAISearch = async (searchQuery: string) => {
    try {
      setAiLoading(true);
      
      // Simulate AI search - replace with actual AI API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock AI search results
      const mockAiResults: AISearchResult[] = [
        {
          id: "1",
          name: "Indomie Goreng Original",
          price: 3000,
          category: "Makanan Instan",
          relevanceScore: 0.95,
          matchReason: "Exact product name match",
        },
        {
          id: "2",
          name: "Indomie Soto Ayam",
          price: 3200,
          category: "Makanan Instan",
          relevanceScore: 0.87,
          matchReason: "Same brand and category",
        },
        {
          id: "3",
          name: "Mie Sedaap Goreng",
          price: 2800,
          category: "Makanan Instan",
          relevanceScore: 0.72,
          matchReason: "Similar product type",
        },
      ];

      setAiResults(mockAiResults);
      onSearch(searchQuery);
      setShowSuggestions(false);
      
      toast.success(`AI found ${mockAiResults.length} relevant products`);
    } catch (error) {
      console.error("Error in AI search:", error);
      toast.error("AI search failed");
    } finally {
      setAiLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    setShowSuggestions(false);
    
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    } else {
      handleAISearch(suggestion.text);
    }
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case "product":
        return (
          <svg className="w-4 h-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        );
      case "category":
        return (
          <svg className="w-4 h-4 text-success-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
        );
      case "brand":
        return (
          <svg className="w-4 h-4 text-warning-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      case "query":
        return (
          <svg className="w-4 h-4 text-info-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <SearchInput
        placeholder="Search products with AI..."
        value={query}
        onSearch={handleAISearch}
        loading={aiLoading}
        onChange={(e) => setQuery(e.target.value)}
        onFocus={() => query.length > 0 && setShowSuggestions(true)}
      />

      {/* Search Suggestions */}
      {showSuggestions && (suggestions.length > 0 || loading) && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-96 overflow-y-auto">
          <CardContent className="p-0">
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-sm text-secondary-600">Loading suggestions...</span>
              </div>
            ) : (
              <div className="py-2">
                {suggestions.map((suggestion) => (
                  <button
                    key={suggestion.id}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-3 text-left hover:bg-secondary-50 transition-colors flex items-center space-x-3"
                  >
                    {getSuggestionIcon(suggestion.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-secondary-900">
                          {suggestion.text}
                        </span>
                        <Badge variant="secondary" size="sm">
                          {suggestion.type}
                        </Badge>
                        {suggestion.count && (
                          <span className="text-xs text-secondary-500">
                            ({suggestion.count})
                          </span>
                        )}
                      </div>
                      {suggestion.description && (
                        <p className="text-sm text-secondary-600 truncate">
                          {suggestion.description}
                        </p>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* AI Search Results Preview */}
      {aiResults.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-40">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-secondary-900">AI Search Results</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAiResults([])}
              >
                ×
              </Button>
            </div>
            <div className="space-y-2">
              {aiResults.slice(0, 3).map((result) => (
                <div
                  key={result.id}
                  className="flex items-center justify-between p-2 bg-secondary-50 rounded-lg"
                >
                  <div className="flex-1">
                    <p className="font-medium text-secondary-900">{result.name}</p>
                    <p className="text-sm text-secondary-600">{result.matchReason}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-primary-600">
                      {formatCurrency(result.price)}
                    </p>
                    <p className="text-xs text-secondary-500">
                      {Math.round(result.relevanceScore * 100)}% match
                    </p>
                  </div>
                </div>
              ))}
              {aiResults.length > 3 && (
                <p className="text-sm text-secondary-600 text-center">
                  +{aiResults.length - 3} more results
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AISearchBar;
