import { ROLE_PERMISSIONS } from "@/config/constants";
import prisma from "@/db/client";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { compare } from "bcryptjs";
import { NextAuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { loginSchema } from "./validations";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            throw new Error("Email and password are required");
          }

          // Validate input
          const validatedFields = loginSchema.safeParse(credentials);
          if (!validatedFields.success) {
            throw new Error("Invalid email or password format");
          }

          const { email, password } = validatedFields.data;

          // Find user with role
          const user = await prisma.user.findUnique({
            where: { email },
            include: {
              role: true,
            },
          });

          if (!user) {
            throw new Error("Invalid email or password");
          }

          // Verify password
          const isPasswordValid = await compare(password, user.password);
          if (!isPasswordValid) {
            throw new Error("Invalid email or password");
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role.name,
            permissions: user.role.permissions,
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.permissions = user.permissions;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.permissions = token.permissions as string[];
      }
      return session;
    },
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log(`User ${user.email} signed in`);

      // Create login notification
      try {
        await prisma.notification.create({
          data: {
            title: "User Login",
            message: `${user.name} logged in to the system`,
            type: "INFO",
            priority: "LOW",
            userId: user.id,
            metadata: {
              loginTime: new Date().toISOString(),
              userAgent: "Unknown", // You can get this from request headers
            },
          },
        });
      } catch (error) {
        console.error("Error creating login notification:", error);
      }
    },
    async signOut({ session, token }) {
      console.log(`User signed out`);
    },
  },
};

// Helper function to check if user has permission
export function hasPermission(
  userPermissions: string[],
  requiredPermission: string
): boolean {
  return userPermissions.includes(requiredPermission);
}

// Helper function to check if user has any of the required permissions
export function hasAnyPermission(
  userPermissions: string[],
  requiredPermissions: string[]
): boolean {
  return requiredPermissions.some((permission) =>
    userPermissions.includes(permission)
  );
}

// Helper function to check if user has all required permissions
export function hasAllPermissions(
  userPermissions: string[],
  requiredPermissions: string[]
): boolean {
  return requiredPermissions.every((permission) =>
    userPermissions.includes(permission)
  );
}

// Helper function to get user role permissions
export function getRolePermissions(role: string): string[] {
  return Array.from(
    ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || []
  );
}

// Helper function to check if user can access resource
export function canAccessResource(
  userRole: string,
  requiredRoles: string[]
): boolean {
  return requiredRoles.includes(userRole);
}

// Middleware function to check permissions
export function requirePermission(permission: string) {
  return (userPermissions: string[]) => {
    if (!hasPermission(userPermissions, permission)) {
      throw new Error(`Access denied. Required permission: ${permission}`);
    }
    return true;
  };
}

// Middleware function to check role
export function requireRole(role: string) {
  return (userRole: string) => {
    if (userRole !== role) {
      throw new Error(`Access denied. Required role: ${role}`);
    }
    return true;
  };
}

// Middleware function to check any of the roles
export function requireAnyRole(roles: string[]) {
  return (userRole: string) => {
    if (!roles.includes(userRole)) {
      throw new Error(`Access denied. Required roles: ${roles.join(", ")}`);
    }
    return true;
  };
}

// Function to create user with role
export async function createUserWithRole(userData: {
  email: string;
  name: string;
  password: string;
  roleName: string;
}) {
  try {
    // Find role
    const role = await prisma.role.findUnique({
      where: { name: userData.roleName },
    });

    if (!role) {
      throw new Error(`Role ${userData.roleName} not found`);
    }

    // Hash password
    const bcrypt = require("bcryptjs");
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: userData.email,
        name: userData.name,
        password: hashedPassword,
        roleId: role.id,
      },
      include: {
        role: true,
      },
    });

    // Create welcome notification
    await prisma.notification.create({
      data: {
        title: "Welcome to Arta Boga",
        message: `Welcome ${user.name}! Your account has been created successfully.`,
        type: "SUCCESS",
        priority: "MEDIUM",
        userId: user.id,
        metadata: {
          accountCreated: new Date().toISOString(),
          role: role.name,
        },
      },
    });

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role.name,
      permissions: user.role.permissions,
    };
  } catch (error) {
    console.error("Error creating user:", error);
    throw error;
  }
}

// Function to update user role
export async function updateUserRole(userId: string, newRoleName: string) {
  try {
    const role = await prisma.role.findUnique({
      where: { name: newRoleName },
    });

    if (!role) {
      throw new Error(`Role ${newRoleName} not found`);
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { roleId: role.id },
      include: {
        role: true,
      },
    });

    // Create role change notification
    await prisma.notification.create({
      data: {
        title: "Role Updated",
        message: `Your role has been updated to ${role.name}`,
        type: "INFO",
        priority: "MEDIUM",
        userId: userId,
        metadata: {
          oldRole: "Unknown", // You might want to track this
          newRole: role.name,
          updatedAt: new Date().toISOString(),
        },
      },
    });

    return updatedUser;
  } catch (error) {
    console.error("Error updating user role:", error);
    throw error;
  }
}

// Function to get user with permissions
export async function getUserWithPermissions(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        role: true,
      },
    });

    if (!user) {
      throw new Error("User not found");
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role.name,
      permissions: user.role.permissions,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  } catch (error) {
    console.error("Error getting user with permissions:", error);
    throw error;
  }
}
