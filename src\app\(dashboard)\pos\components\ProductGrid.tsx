"use client";

import React from "react";
import Image from "next/image";
import { <PERSON>, CardContent, Badge, Button } from "@/components/ui";
import { formatCurrency } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  category: string;
  barcode?: string;
  image?: string;
}

interface ProductGridProps {
  products: Product[];
  onAddToCart: (product: Product, quantity?: number) => void;
}

const ProductGrid: React.FC<ProductGridProps> = ({ products, onAddToCart }) => {
  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="error" size="sm">Out of Stock</Badge>;
    } else if (stock <= 10) {
      return <Badge variant="warning" size="sm">Low Stock</Badge>;
    } else {
      return <Badge variant="success" size="sm">In Stock</Badge>;
    }
  };

  if (products.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-medium text-secondary-900 mb-2">
            No products found
          </h3>
          <p className="text-secondary-600">
            Try adjusting your search or category filter
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {products.map((product) => (
        <Card
          key={product.id}
          className="group hover:shadow-lg transition-all duration-200 cursor-pointer"
          onClick={() => product.stock > 0 && onAddToCart(product)}
        >
          <CardContent className="p-3">
            {/* Product Image */}
            <div className="aspect-square relative overflow-hidden rounded-lg bg-secondary-50 mb-3">
              <Image
                src={product.image || "/api/placeholder/150/150"}
                alt={product.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-200"
              />
              
              {/* Stock Badge */}
              <div className="absolute top-2 left-2">
                {getStockBadge(product.stock)}
              </div>

              {/* Quick Add Button */}
              {product.stock > 0 && (
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                  <Button
                    size="sm"
                    className="bg-white text-secondary-900 hover:bg-secondary-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddToCart(product);
                    }}
                  >
                    Add to Cart
                  </Button>
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-2">
              {/* Product Name */}
              <h3 className="font-medium text-secondary-900 text-sm line-clamp-2 min-h-[2.5rem]">
                {product.name}
              </h3>

              {/* Price */}
              <div className="text-lg font-bold text-primary-600">
                {formatCurrency(product.price)}
              </div>

              {/* Stock Info */}
              <div className="flex items-center justify-between text-xs text-secondary-600">
                <span>Stock: {product.stock}</span>
                <span className="truncate ml-2">{product.category}</span>
              </div>

              {/* Barcode */}
              {product.barcode && (
                <div className="text-xs text-secondary-500 font-mono">
                  {product.barcode}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="mt-3 space-y-2">
              <Button
                variant="primary"
                size="sm"
                className="w-full"
                disabled={product.stock === 0}
                onClick={(e) => {
                  e.stopPropagation();
                  onAddToCart(product);
                }}
              >
                {product.stock === 0 ? "Out of Stock" : "Add to Cart"}
              </Button>

              {/* Quick quantity buttons for fast-moving items */}
              {product.stock > 1 && (
                <div className="grid grid-cols-3 gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs py-1"
                    disabled={product.stock < 2}
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddToCart(product, 2);
                    }}
                  >
                    +2
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs py-1"
                    disabled={product.stock < 5}
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddToCart(product, 5);
                    }}
                  >
                    +5
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs py-1"
                    disabled={product.stock < 10}
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddToCart(product, 10);
                    }}
                  >
                    +10
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ProductGrid;
