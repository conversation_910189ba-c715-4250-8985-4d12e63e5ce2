"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Table,
  <PERSON>ton,
  Badge,
  Modal,
  Loading<PERSON>pinner,
  EmptyState,
  Pagination,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import ProductForm from "./components/ProductForm";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

const ProductsPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Modal states
  const [showProductModal, setShowProductModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingProduct, setDeletingProduct] = useState<Product | null>(null);

  const canCreate = checkPermission(PERMISSIONS.PRODUCT_CREATE);
  const canUpdate = checkPermission(PERMISSIONS.PRODUCT_UPDATE);
  const canDelete = checkPermission(PERMISSIONS.PRODUCT_DELETE);

  useEffect(() => {
    fetchProducts();
  }, [currentPage, itemsPerPage, searchQuery]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API response
      const mockProducts: Product[] = [
        {
          id: "1",
          name: "Chitato Rasa Sapi Panggang",
          description: "Keripik kentang rasa sapi panggang 68g",
          price: 8500,
          stock: 100,
          category: { id: "1", name: "Makanan Ringan" },
          warehouse: { id: "1", name: "Gudang Utama" },
          createdAt: "2024-01-15T10:30:00Z",
          updatedAt: "2024-01-15T10:30:00Z",
        },
        {
          id: "2",
          name: "Teh Botol Sosro",
          description: "Minuman teh dalam kemasan botol 450ml",
          price: 4500,
          stock: 8,
          category: { id: "2", name: "Minuman" },
          warehouse: { id: "1", name: "Gudang Utama" },
          createdAt: "2024-01-15T09:15:00Z",
          updatedAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "3",
          name: "Indomie Goreng",
          description: "Mie instan goreng rasa original",
          price: 3000,
          stock: 5,
          category: { id: "3", name: "Makanan Instan" },
          warehouse: { id: "1", name: "Gudang Utama" },
          createdAt: "2024-01-14T16:45:00Z",
          updatedAt: "2024-01-14T16:45:00Z",
        },
      ];

      setProducts(mockProducts);
      setTotalItems(mockProducts.length);
      setTotalPages(Math.ceil(mockProducts.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProduct = () => {
    setEditingProduct(null);
    setShowProductModal(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowProductModal(true);
  };

  const handleDeleteProduct = (product: Product) => {
    setDeletingProduct(product);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!deletingProduct) return;

    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProducts(prev => prev.filter(p => p.id !== deletingProduct.id));
      toast.success("Product deleted successfully");
      setShowDeleteModal(false);
      setDeletingProduct(null);
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    }
  };

  const handleProductSave = async (productData: any) => {
    try {
      if (editingProduct) {
        // Update existing product
        setProducts(prev => 
          prev.map(p => 
            p.id === editingProduct.id 
              ? { ...p, ...productData, updatedAt: new Date().toISOString() }
              : p
          )
        );
        toast.success("Product updated successfully");
      } else {
        // Create new product
        const newProduct: Product = {
          id: Date.now().toString(),
          ...productData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setProducts(prev => [newProduct, ...prev]);
        toast.success("Product created successfully");
      }
      
      setShowProductModal(false);
      setEditingProduct(null);
    } catch (error) {
      console.error("Error saving product:", error);
      toast.error("Failed to save product");
    }
  };

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="error">Out of Stock</Badge>;
    } else if (stock <= 10) {
      return <Badge variant="warning">Low Stock ({stock})</Badge>;
    } else {
      return <Badge variant="success">In Stock ({stock})</Badge>;
    }
  };

  const columns = [
    {
      key: "name",
      title: "Product Name",
      dataIndex: "name",
      sortable: true,
    },
    {
      key: "category",
      title: "Category",
      dataIndex: "category",
      render: (category: any) => category.name,
    },
    {
      key: "price",
      title: "Price",
      dataIndex: "price",
      render: (price: number) => formatCurrency(price),
      sortable: true,
    },
    {
      key: "stock",
      title: "Stock",
      dataIndex: "stock",
      render: (stock: number) => getStockBadge(stock),
      sortable: true,
    },
    {
      key: "warehouse",
      title: "Warehouse",
      dataIndex: "warehouse",
      render: (warehouse: any) => warehouse.name,
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, product: Product) => (
        <div className="flex space-x-2">
          {canUpdate && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEditProduct(product)}
            >
              Edit
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteProduct(product)}
            >
              Delete
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Products</h1>
          <p className="text-secondary-600">Manage your product inventory</p>
        </div>
        {canCreate && (
          <Button onClick={handleCreateProduct}>
            Add Product
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search products..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Button variant="outline">
              Filter
            </Button>
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Products ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading products..." />
            </div>
          ) : products.length === 0 ? (
            <EmptyState
              title="No products found"
              description="Get started by adding your first product"
              action={canCreate ? {
                label: "Add Product",
                onClick: handleCreateProduct,
              } : undefined}
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={products}
                loading={loading}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Product Form Modal */}
      <Modal
        isOpen={showProductModal}
        onClose={() => {
          setShowProductModal(false);
          setEditingProduct(null);
        }}
        title={editingProduct ? "Edit Product" : "Add New Product"}
        size="lg"
      >
        <ProductForm
          product={editingProduct}
          onSave={handleProductSave}
          onCancel={() => {
            setShowProductModal(false);
            setEditingProduct(null);
          }}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingProduct(null);
        }}
        title="Delete Product"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-secondary-700">
            Are you sure you want to delete "{deletingProduct?.name}"? This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteModal(false);
                setDeletingProduct(null);
              }}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ProductsPage;
