import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create roles
  console.log('👥 Creating roles...');
  const ownerRole = await prisma.role.upsert({
    where: { name: 'owner' },
    update: {},
    create: {
      name: 'owner',
      permissions: [
        'product:create', 'product:read', 'product:update', 'product:delete',
        'order:create', 'order:read', 'order:update', 'order:delete',
        'customer:create', 'customer:read', 'customer:update', 'customer:delete',
        'user:create', 'user:read', 'user:update', 'user:delete',
        'inventory:read', 'inventory:update',
        'reports:read', 'reports:export',
        'system:config', 'system:backup'
      ],
    },
  });

  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      permissions: [
        'product:create', 'product:read', 'product:update', 'product:delete',
        'order:create', 'order:read', 'order:update',
        'customer:create', 'customer:read', 'customer:update',
        'user:read',
        'inventory:read', 'inventory:update',
        'reports:read', 'reports:export'
      ],
    },
  });

  const salesRole = await prisma.role.upsert({
    where: { name: 'sales' },
    update: {},
    create: {
      name: 'sales',
      permissions: [
        'product:read',
        'order:create', 'order:read', 'order:update',
        'customer:create', 'customer:read', 'customer:update',
        'inventory:read'
      ],
    },
  });

  const cashierRole = await prisma.role.upsert({
    where: { name: 'cashier' },
    update: {},
    create: {
      name: 'cashier',
      permissions: [
        'product:read',
        'order:create', 'order:read',
        'customer:read',
        'inventory:read'
      ],
    },
  });

  const customerRole = await prisma.role.upsert({
    where: { name: 'customer' },
    update: {},
    create: {
      name: 'customer',
      permissions: ['product:read', 'order:read'],
    },
  });

  // Create default users
  console.log('👤 Creating default users...');
  const hashedPassword = await hash('password123', 12);

  const owner = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Owner Arta Boga',
      password: hashedPassword,
      roleId: ownerRole.id,
    },
  });

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin Arta Boga',
      password: hashedPassword,
      roleId: adminRole.id,
    },
  });

  // Create warehouses
  console.log('🏢 Creating warehouses...');
  const mainWarehouse = await prisma.warehouse.upsert({
    where: { id: 'main-warehouse' },
    update: {},
    create: {
      id: 'main-warehouse',
      name: 'Gudang Utama',
      address: 'Jl. Raya Industri No. 123, Jakarta',
    },
  });

  const branchWarehouse = await prisma.warehouse.upsert({
    where: { id: 'branch-warehouse' },
    update: {},
    create: {
      id: 'branch-warehouse',
      name: 'Gudang Cabang',
      address: 'Jl. Perdagangan No. 456, Surabaya',
    },
  });

  // Create categories
  console.log('📂 Creating categories...');
  const categories = [
    { name: 'Makanan Ringan', id: 'snacks' },
    { name: 'Minuman', id: 'beverages' },
    { name: 'Makanan Kaleng', id: 'canned-food' },
    { name: 'Bumbu Dapur', id: 'spices' },
    { name: 'Produk Susu', id: 'dairy' },
    { name: 'Roti & Kue', id: 'bakery' },
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { name: category.name },
      update: {},
      create: {
        id: category.id,
        name: category.name,
      },
    });
  }

  // Create sample products
  console.log('📦 Creating sample products...');
  const products = [
    {
      name: 'Chitato Rasa Sapi Panggang',
      description: 'Keripik kentang rasa sapi panggang 68g',
      price: 8500,
      stock: 100,
      categoryId: 'snacks',
      warehouseId: mainWarehouse.id,
    },
    {
      name: 'Teh Botol Sosro',
      description: 'Minuman teh dalam kemasan botol 450ml',
      price: 4500,
      stock: 200,
      categoryId: 'beverages',
      warehouseId: mainWarehouse.id,
    },
    {
      name: 'Indomie Goreng',
      description: 'Mie instan goreng rasa original',
      price: 3000,
      stock: 500,
      categoryId: 'canned-food',
      warehouseId: mainWarehouse.id,
    },
    {
      name: 'Royco Kaldu Ayam',
      description: 'Penyedap rasa kaldu ayam 100g',
      price: 12000,
      stock: 75,
      categoryId: 'spices',
      warehouseId: branchWarehouse.id,
    },
  ];

  for (const product of products) {
    await prisma.product.create({
      data: product,
    });
  }

  // Create sample customers
  console.log('👥 Creating sample customers...');
  const customers = [
    {
      name: 'Toko Berkah Jaya',
      email: '<EMAIL>',
      phone: '081234567890',
      address: 'Jl. Mawar No. 15, Jakarta Selatan',
    },
    {
      name: 'Warung Maju Mundur',
      email: '<EMAIL>',
      phone: '081234567891',
      address: 'Jl. Melati No. 22, Bandung',
    },
  ];

  for (const customer of customers) {
    await prisma.customer.create({
      data: customer,
    });
  }

  // Create system settings
  console.log('⚙️ Creating system settings...');
  const settings = [
    { key: 'app_name', value: 'AI Distributor Agent - Arta Boga', type: 'string' },
    { key: 'currency', value: 'IDR', type: 'string' },
    { key: 'tax_rate', value: '0.11', type: 'number' },
    { key: 'low_stock_threshold', value: '10', type: 'number' },
    { key: 'enable_notifications', value: 'true', type: 'boolean' },
  ];

  for (const setting of settings) {
    await prisma.systemSettings.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }

  console.log('✅ Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
