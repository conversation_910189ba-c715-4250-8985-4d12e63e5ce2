"use client";

import React from "react";
import {
  <PERSON>,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
  CardContent,
  Button,
  Badge,
  Input,
} from "@/components/ui";
import { formatCurrency } from "@/lib/utils";

interface CartItem {
  productId: string;
  productName: string;
  price: number;
  quantity: number;
  subtotal: number;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  discount?: number;
}

interface ShoppingCartProps {
  items: CartItem[];
  customer: Customer | null;
  onUpdateItem: (productId: string, quantity: number) => void;
  onRemoveItem: (productId: string) => void;
  onClearCart: () => void;
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  onCheckout: () => void;
  disabled: boolean;
}

const ShoppingCart: React.FC<ShoppingCartProps> = ({
  items,
  customer,
  onUpdateItem,
  onRemoveItem,
  onClearCart,
  subtotal,
  discount,
  tax,
  total,
  onCheckout,
  disabled,
}) => {
  return (
    <div className="flex flex-col h-full">
      {/* <PERSON>t <PERSON>er */}
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <span>Cart</span>
            <Badge variant="primary">{items.length}</Badge>
          </CardTitle>
          {items.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearCart}
              className="text-error-600 hover:text-error-700"
            >
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>

      {/* Customer Info */}
      {customer && (
        <div className="p-4 bg-primary-50 border-b">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-primary-900">{customer.name}</p>
              {customer.phone && (
                <p className="text-sm text-primary-700">{customer.phone}</p>
              )}
            </div>
            {customer.discount && (
              <Badge variant="success">
                {customer.discount}% Discount
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Cart Items */}
      <CardContent className="flex-1 overflow-auto p-0">
        {items.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-6xl mb-4">🛒</div>
              <h3 className="text-lg font-medium text-secondary-900 mb-2">
                Cart is empty
              </h3>
              <p className="text-secondary-600">
                Add products to start a sale
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-secondary-200">
            {items.map((item) => (
              <div key={item.productId} className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-secondary-900 text-sm line-clamp-2">
                      {item.productName}
                    </h4>
                    <p className="text-sm text-secondary-600">
                      {formatCurrency(item.price)} each
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveItem(item.productId)}
                    className="text-error-600 hover:text-error-700 ml-2"
                  >
                    ×
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onUpdateItem(item.productId, item.quantity - 1)}
                      disabled={item.quantity <= 1}
                      className="w-8 h-8 p-0"
                    >
                      -
                    </Button>
                    <Input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => {
                        const quantity = parseInt(e.target.value) || 1;
                        onUpdateItem(item.productId, quantity);
                      }}
                      className="w-16 text-center text-sm"
                      min="1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onUpdateItem(item.productId, item.quantity + 1)}
                      className="w-8 h-8 p-0"
                    >
                      +
                    </Button>
                  </div>

                  {/* Subtotal */}
                  <div className="font-medium text-secondary-900">
                    {formatCurrency(item.subtotal)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Cart Summary */}
      {items.length > 0 && (
        <div className="border-t bg-secondary-50">
          <div className="p-4 space-y-3">
            {/* Subtotal */}
            <div className="flex justify-between text-sm">
              <span className="text-secondary-600">Subtotal:</span>
              <span className="font-medium">{formatCurrency(subtotal)}</span>
            </div>

            {/* Discount */}
            {discount > 0 && (
              <div className="flex justify-between text-sm text-success-600">
                <span>Discount:</span>
                <span className="font-medium">-{formatCurrency(discount)}</span>
              </div>
            )}

            {/* Tax */}
            <div className="flex justify-between text-sm">
              <span className="text-secondary-600">Tax (10%):</span>
              <span className="font-medium">{formatCurrency(tax)}</span>
            </div>

            {/* Total */}
            <div className="flex justify-between text-lg font-bold border-t pt-3">
              <span>Total:</span>
              <span className="text-primary-600">{formatCurrency(total)}</span>
            </div>

            {/* Checkout Button */}
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={onCheckout}
              disabled={disabled}
            >
              Proceed to Payment
            </Button>

            {/* Quick Actions */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Hold transaction functionality
                  console.log("Hold transaction");
                }}
              >
                Hold
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Print receipt functionality
                  console.log("Print receipt");
                }}
              >
                Print
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShoppingCart;
