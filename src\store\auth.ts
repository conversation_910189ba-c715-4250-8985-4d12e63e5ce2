import { User } from "@prisma/client";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AuthUser extends Omit<User, "password"> {
  role: string;
  permissions: string[];
}

interface AuthStore {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  lastActivity: number;

  // Actions
  setUser: (user: AuthUser | null) => void;
  setLoading: (loading: boolean) => void;
  setAuthenticated: (authenticated: boolean) => void;
  updateLastActivity: () => void;
  logout: () => void;

  // Permission checks
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      lastActivity: Date.now(),

      // Actions
      setUser: (user) =>
        set({
          user,
          isAuthenticated: !!user,
          lastActivity: Date.now(),
        }),

      setLoading: (loading) => set({ isLoading: loading }),

      setAuthenticated: (authenticated) =>
        set({ isAuthenticated: authenticated }),

      updateLastActivity: () => set({ lastActivity: Date.now() }),

      logout: () =>
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          lastActivity: Date.now(),
        }),

      // Permission checks
      hasPermission: (permission: string) => {
        const { user } = get();
        return user?.permissions?.includes(permission) || false;
      },

      hasAnyPermission: (permissions: string[]) => {
        const { user } = get();
        if (!user?.permissions) return false;
        return permissions.some((permission) =>
          user.permissions.includes(permission)
        );
      },

      hasRole: (role: string) => {
        const { user } = get();
        return user?.role === role;
      },

      hasAnyRole: (roles: string[]) => {
        const { user } = get();
        return user?.role ? roles.includes(user.role) : false;
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        lastActivity: state.lastActivity,
      }),
    }
  )
);
