"use client";

import React, { useState, useEffect } from "react";
import { Button, Input, Badge } from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  discount?: number;
  totalPurchases?: number;
  lastPurchase?: string;
}

interface CustomerModalProps {
  selectedCustomer: Customer | null;
  onSelectCustomer: (customer: Customer | null) => void;
  onCancel: () => void;
}

const CustomerModal: React.FC<CustomerModalProps> = ({
  selectedCustomer,
  onSelectCustomer,
  onCancel,
}) => {
  const toast = useToastActions();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    phone: "",
    email: "",
  });

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock customers data
      setCustomers([
        {
          id: "1",
          name: "Toko Berkah Jaya",
          phone: "+62812-3456-7890",
          email: "<EMAIL>",
          discount: 5,
          totalPurchases: 15750000,
          lastPurchase: "2024-01-15",
        },
        {
          id: "2",
          name: "Warung Maju Mundur",
          phone: "+62813-4567-8901",
          email: "<EMAIL>",
          discount: 3,
          totalPurchases: 8950000,
          lastPurchase: "2024-01-14",
        },
        {
          id: "3",
          name: "Toko Sumber Rejeki",
          phone: "+62814-5678-9012",
          email: "<EMAIL>",
          discount: 10,
          totalPurchases: 25600000,
          lastPurchase: "2024-01-13",
        },
        {
          id: "4",
          name: "Minimarket Sejahtera",
          phone: "+62815-6789-0123",
          totalPurchases: 3200000,
          lastPurchase: "2024-01-12",
        },
        {
          id: "5",
          name: "Toko Bahagia",
          phone: "+62816-7890-1234",
          email: "<EMAIL>",
          discount: 2,
          totalPurchases: 1200000,
          lastPurchase: "2024-01-10",
        },
      ]);
    } catch (error) {
      console.error("Error fetching customers:", error);
      toast.error("Failed to load customers");
    } finally {
      setLoading(false);
    }
  };

  const handleAddCustomer = async () => {
    if (!newCustomer.name.trim()) {
      toast.error("Customer name is required");
      return;
    }

    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const customer: Customer = {
        id: Date.now().toString(),
        name: newCustomer.name.trim(),
        phone: newCustomer.phone.trim() || undefined,
        email: newCustomer.email.trim() || undefined,
        totalPurchases: 0,
      };

      setCustomers(prev => [customer, ...prev]);
      setNewCustomer({ name: "", phone: "", email: "" });
      setShowAddForm(false);
      toast.success("Customer added successfully");
    } catch (error) {
      console.error("Error adding customer:", error);
      toast.error("Failed to add customer");
    }
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phone?.includes(searchQuery) ||
    customer.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getCustomerBadge = (customer: Customer) => {
    if (customer.discount && customer.discount > 0) {
      return (
        <Badge variant="success" size="sm">
          {customer.discount}% Discount
        </Badge>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      {/* Search and Actions */}
      <div className="flex items-center space-x-3">
        <div className="flex-1">
          <SearchInput
            placeholder="Search customers..."
            value={searchQuery}
            onSearch={setSearchQuery}
          />
        </div>
        <Button
          variant="outline"
          onClick={() => setShowAddForm(!showAddForm)}
        >
          {showAddForm ? "Cancel" : "Add New"}
        </Button>
      </div>

      {/* Add Customer Form */}
      {showAddForm && (
        <div className="p-4 bg-secondary-50 rounded-lg space-y-3">
          <h4 className="font-medium text-secondary-900">Add New Customer</h4>
          <div className="grid grid-cols-1 gap-3">
            <Input
              placeholder="Customer name *"
              value={newCustomer.name}
              onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
            />
            <Input
              placeholder="Phone number"
              value={newCustomer.phone}
              onChange={(e) => setNewCustomer(prev => ({ ...prev, phone: e.target.value }))}
            />
            <Input
              placeholder="Email address"
              type="email"
              value={newCustomer.email}
              onChange={(e) => setNewCustomer(prev => ({ ...prev, email: e.target.value }))}
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant="primary"
              size="sm"
              onClick={handleAddCustomer}
              disabled={!newCustomer.name.trim()}
            >
              Add Customer
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShowAddForm(false);
                setNewCustomer({ name: "", phone: "", email: "" });
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Walk-in Customer Option */}
      <div
        className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
          !selectedCustomer
            ? "border-primary-500 bg-primary-50"
            : "border-secondary-200 hover:border-secondary-300"
        }`}
        onClick={() => onSelectCustomer(null)}
      >
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-secondary-900">Walk-in Customer</h3>
            <p className="text-sm text-secondary-600">No customer information required</p>
          </div>
          {!selectedCustomer && (
            <Badge variant="primary">Selected</Badge>
          )}
        </div>
      </div>

      {/* Customer List */}
      <div className="max-h-96 overflow-y-auto space-y-2">
        {loading ? (
          <div className="text-center py-8">
            <div className="text-secondary-600">Loading customers...</div>
          </div>
        ) : filteredCustomers.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-secondary-600">No customers found</div>
          </div>
        ) : (
          filteredCustomers.map((customer) => (
            <div
              key={customer.id}
              className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                selectedCustomer?.id === customer.id
                  ? "border-primary-500 bg-primary-50"
                  : "border-secondary-200 hover:border-secondary-300"
              }`}
              onClick={() => onSelectCustomer(customer)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-medium text-secondary-900">{customer.name}</h3>
                    {getCustomerBadge(customer)}
                    {selectedCustomer?.id === customer.id && (
                      <Badge variant="primary">Selected</Badge>
                    )}
                  </div>
                  
                  <div className="space-y-1 text-sm text-secondary-600">
                    {customer.phone && (
                      <div className="flex items-center space-x-1">
                        <span>📞</span>
                        <span>{customer.phone}</span>
                      </div>
                    )}
                    {customer.email && (
                      <div className="flex items-center space-x-1">
                        <span>📧</span>
                        <span>{customer.email}</span>
                      </div>
                    )}
                    {customer.totalPurchases && (
                      <div className="flex items-center space-x-1">
                        <span>💰</span>
                        <span>Total: Rp {customer.totalPurchases.toLocaleString()}</span>
                      </div>
                    )}
                    {customer.lastPurchase && (
                      <div className="flex items-center space-x-1">
                        <span>📅</span>
                        <span>Last: {customer.lastPurchase}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3 pt-4 border-t border-secondary-200">
        <Button
          variant="outline"
          onClick={onCancel}
          className="flex-1"
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={() => onSelectCustomer(selectedCustomer)}
          className="flex-1"
        >
          Confirm Selection
        </Button>
      </div>
    </div>
  );
};

export default CustomerModal;
