"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Stats,
  Button,
  Select,
  LoadingSpinner,
  Table,
  Badge,
} from "@/components/ui";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import { formatCurrency, formatDate, formatPercentage } from "@/lib/utils";

interface ReportData {
  salesSummary: {
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    revenueGrowth: number;
  };
  topProducts: Array<{
    id: string;
    name: string;
    category: string;
    revenue: number;
    quantity: number;
  }>;
  topCustomers: Array<{
    id: string;
    name: string;
    orders: number;
    revenue: number;
  }>;
  inventoryStatus: {
    totalProducts: number;
    lowStockItems: number;
    outOfStockItems: number;
    totalValue: number;
  };
}

const ReportsPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("30");
  const [selectedReport, setSelectedReport] = useState("overview");

  const canViewReports = checkPermission(PERMISSIONS.REPORTS_READ);

  useEffect(() => {
    if (canViewReports) {
      fetchReportData();
    }
  }, [selectedPeriod, canViewReports]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock data - replace with actual API response
      setReportData({
        salesSummary: {
          totalRevenue: 125430000,
          totalOrders: 1234,
          averageOrderValue: 101650,
          revenueGrowth: 12.5,
        },
        topProducts: [
          {
            id: "1",
            name: "Indomie Goreng",
            category: "Makanan Instan",
            revenue: 15750000,
            quantity: 5250,
          },
          {
            id: "2",
            name: "Teh Botol Sosro",
            category: "Minuman",
            revenue: 12300000,
            quantity: 2733,
          },
          {
            id: "3",
            name: "Chitato Sapi Panggang",
            category: "Makanan Ringan",
            revenue: 8950000,
            quantity: 1053,
          },
        ],
        topCustomers: [
          {
            id: "1",
            name: "Toko Berkah Jaya",
            orders: 25,
            revenue: 15750000,
          },
          {
            id: "2",
            name: "Warung Maju Mundur",
            orders: 18,
            revenue: 8900000,
          },
          {
            id: "3",
            name: "Toko Sumber Rejeki",
            orders: 32,
            revenue: 22400000,
          },
        ],
        inventoryStatus: {
          totalProducts: 89,
          lowStockItems: 12,
          outOfStockItems: 3,
          totalValue: 45600000,
        },
      });
    } catch (error) {
      console.error("Error fetching report data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!canViewReports) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-secondary-900 mb-2">
            Access Denied
          </h2>
          <p className="text-secondary-600">
            You don't have permission to view reports.
          </p>
        </div>
      </div>
    );
  }

  const periodOptions = [
    { value: "7", label: "Last 7 days" },
    { value: "30", label: "Last 30 days" },
    { value: "90", label: "Last 3 months" },
    { value: "365", label: "Last year" },
  ];

  const reportOptions = [
    { value: "overview", label: "Overview" },
    { value: "sales", label: "Sales Report" },
    { value: "inventory", label: "Inventory Report" },
    { value: "customers", label: "Customer Report" },
  ];

  const productColumns = [
    {
      key: "name",
      title: "Product",
      dataIndex: "name",
    },
    {
      key: "category",
      title: "Category",
      dataIndex: "category",
    },
    {
      key: "quantity",
      title: "Quantity Sold",
      dataIndex: "quantity",
      render: (value: number) => value.toLocaleString(),
    },
    {
      key: "revenue",
      title: "Revenue",
      dataIndex: "revenue",
      render: (value: number) => formatCurrency(value),
    },
  ];

  const customerColumns = [
    {
      key: "name",
      title: "Customer",
      dataIndex: "name",
    },
    {
      key: "orders",
      title: "Orders",
      dataIndex: "orders",
      render: (value: number) => value.toLocaleString(),
    },
    {
      key: "revenue",
      title: "Total Revenue",
      dataIndex: "revenue",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "type",
      title: "Type",
      render: (_: any, customer: any) => {
        if (customer.revenue >= 20000000) {
          return <Badge variant="success">VIP</Badge>;
        } else if (customer.revenue >= 10000000) {
          return <Badge variant="primary">Premium</Badge>;
        } else {
          return <Badge variant="default">Regular</Badge>;
        }
      },
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading reports..." />
      </div>
    );
  }

  const statsData = reportData ? [
    {
      label: "Total Revenue",
      value: formatCurrency(reportData.salesSummary.totalRevenue),
      change: {
        value: Math.abs(reportData.salesSummary.revenueGrowth),
        type: reportData.salesSummary.revenueGrowth >= 0 ? "increase" : "decrease",
        period: `vs previous period`,
      },
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: "success" as const,
    },
    {
      label: "Total Orders",
      value: reportData.salesSummary.totalOrders.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Average Order Value",
      value: formatCurrency(reportData.salesSummary.averageOrderValue),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      color: "info" as const,
    },
    {
      label: "Inventory Value",
      value: formatCurrency(reportData.inventoryStatus.totalValue),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: "warning" as const,
    },
  ] : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Reports & Analytics</h1>
          <p className="text-secondary-600">
            Comprehensive business insights and performance metrics
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            Export PDF
          </Button>
          <Button variant="outline">
            Export Excel
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-secondary-700">Period:</label>
              <Select
                options={periodOptions}
                value={selectedPeriod}
                onChange={setSelectedPeriod}
                className="w-40"
              />
            </div>
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-secondary-700">Report:</label>
              <Select
                options={reportOptions}
                value={selectedReport}
                onChange={setSelectedReport}
                className="w-48"
              />
            </div>
            <Button onClick={fetchReportData}>
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Stats stats={statsData} columns={4} />

      {/* Detailed Reports */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Selling Products</CardTitle>
          </CardHeader>
          <CardContent>
            <Table
              columns={productColumns}
              data={reportData?.topProducts || []}
              emptyText="No product data available"
            />
          </CardContent>
        </Card>

        {/* Top Customers */}
        <Card>
          <CardHeader>
            <CardTitle>Top Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <Table
              columns={customerColumns}
              data={reportData?.topCustomers || []}
              emptyText="No customer data available"
            />
          </CardContent>
        </Card>
      </div>

      {/* Inventory Status */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-primary-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-600">
                {reportData?.inventoryStatus.totalProducts || 0}
              </div>
              <div className="text-sm text-primary-700">Total Products</div>
            </div>
            <div className="text-center p-4 bg-warning-50 rounded-lg">
              <div className="text-2xl font-bold text-warning-600">
                {reportData?.inventoryStatus.lowStockItems || 0}
              </div>
              <div className="text-sm text-warning-700">Low Stock Items</div>
            </div>
            <div className="text-center p-4 bg-error-50 rounded-lg">
              <div className="text-2xl font-bold text-error-600">
                {reportData?.inventoryStatus.outOfStockItems || 0}
              </div>
              <div className="text-sm text-error-700">Out of Stock</div>
            </div>
            <div className="text-center p-4 bg-success-50 rounded-lg">
              <div className="text-2xl font-bold text-success-600">
                {formatCurrency(reportData?.inventoryStatus.totalValue || 0)}
              </div>
              <div className="text-sm text-success-700">Total Value</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsPage;
