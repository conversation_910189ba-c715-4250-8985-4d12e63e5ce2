import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";
import { USER_ROLES } from "@/config/constants";

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const pathname = req.nextUrl.pathname;

    // Public routes that don't require authentication
    const publicRoutes = ["/", "/auth/signin", "/auth/signup", "/auth/error"];
    if (publicRoutes.includes(pathname)) {
      return NextResponse.next();
    }

    // API routes protection
    if (pathname.startsWith("/api/")) {
      // Allow auth API routes
      if (pathname.startsWith("/api/auth/")) {
        return NextResponse.next();
      }

      // Require authentication for other API routes
      if (!token) {
        return new NextResponse("Unauthorized", { status: 401 });
      }

      // Role-based API access control
      const userRole = token.role as string;
      
      // Admin-only API routes
      if (pathname.startsWith("/api/admin/") && userRole !== USER_ROLES.OWNER && userRole !== USER_ROLES.ADMIN) {
        return new NextResponse("Forbidden", { status: 403 });
      }

      // Owner-only API routes
      if (pathname.startsWith("/api/owner/") && userRole !== USER_ROLES.OWNER) {
        return new NextResponse("Forbidden", { status: 403 });
      }

      return NextResponse.next();
    }

    // Dashboard routes protection
    if (pathname.startsWith("/dashboard")) {
      if (!token) {
        return NextResponse.redirect(new URL("/auth/signin", req.url));
      }

      const userRole = token.role as string;

      // Role-based dashboard access
      if (pathname.startsWith("/dashboard/admin") && 
          userRole !== USER_ROLES.OWNER && 
          userRole !== USER_ROLES.ADMIN) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      if (pathname.startsWith("/dashboard/owner") && userRole !== USER_ROLES.OWNER) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      if (pathname.startsWith("/dashboard/sales") && 
          userRole !== USER_ROLES.OWNER && 
          userRole !== USER_ROLES.ADMIN && 
          userRole !== USER_ROLES.SALES) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      if (pathname.startsWith("/dashboard/cashier") && 
          userRole !== USER_ROLES.OWNER && 
          userRole !== USER_ROLES.ADMIN && 
          userRole !== USER_ROLES.CASHIER) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      return NextResponse.next();
    }

    // Customer portal protection
    if (pathname.startsWith("/customer")) {
      if (!token) {
        return NextResponse.redirect(new URL("/auth/signin", req.url));
      }

      const userRole = token.role as string;
      if (userRole !== USER_ROLES.CUSTOMER && 
          userRole !== USER_ROLES.OWNER && 
          userRole !== USER_ROLES.ADMIN) {
        return NextResponse.redirect(new URL("/dashboard", req.url));
      }

      return NextResponse.next();
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const pathname = req.nextUrl.pathname;
        
        // Allow public routes
        const publicRoutes = ["/", "/auth/signin", "/auth/signup", "/auth/error"];
        if (publicRoutes.includes(pathname)) {
          return true;
        }

        // Require token for protected routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
