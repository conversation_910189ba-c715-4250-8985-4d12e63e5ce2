"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>eader,
  Card<PERSON>itle,
  CardContent,
  Table,
  <PERSON>ton,
  Badge,
  Modal,
  LoadingSpinner,
  EmptyState,
  Pagination,
  Select,
  Stats,
} from "@/components/ui";
import { SearchInput } from "@/components/forms";
import { useToastActions } from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";
import { PERMISSIONS } from "@/config/constants";
import PaymentModal from "./components/PaymentModal";

interface Payment {
  id: string;
  orderId: string;
  customerName: string;
  amount: number;
  method: "CASH" | "BANK_TRANSFER" | "CREDIT" | "DEBIT_CARD" | "E_WALLET";
  status: "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED";
  reference?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface PaymentStats {
  totalPayments: number;
  completedPayments: number;
  pendingPayments: number;
  totalAmount: number;
}

const PaymentsPage: React.FC = () => {
  const { checkPermission } = useAuth();
  const toast = useToastActions();
  
  const [payments, setPayments] = useState<Payment[]>([]);
  const [stats, setStats] = useState<PaymentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [methodFilter, setMethodFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  const canCreate = checkPermission(PERMISSIONS.ORDER_CREATE);
  const canUpdate = checkPermission(PERMISSIONS.ORDER_UPDATE);

  useEffect(() => {
    fetchPayments();
  }, [currentPage, itemsPerPage, searchQuery, statusFilter, methodFilter]);

  const fetchPayments = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock stats data
      setStats({
        totalPayments: 156,
        completedPayments: 142,
        pendingPayments: 8,
        totalAmount: *********,
      });

      // Mock payments data
      const mockPayments: Payment[] = [
        {
          id: "PAY-001",
          orderId: "ORD-001",
          customerName: "Toko Berkah Jaya",
          amount: 2500000,
          method: "BANK_TRANSFER",
          status: "COMPLETED",
          reference: "TRF-********-001",
          notes: "Payment via BCA transfer",
          createdAt: "2024-01-15T10:30:00Z",
          updatedAt: "2024-01-15T11:00:00Z",
        },
        {
          id: "PAY-002",
          orderId: "ORD-002",
          customerName: "Warung Maju Mundur",
          amount: 1750000,
          method: "CASH",
          status: "COMPLETED",
          createdAt: "2024-01-15T09:15:00Z",
          updatedAt: "2024-01-15T09:15:00Z",
        },
        {
          id: "PAY-003",
          orderId: "ORD-003",
          customerName: "Toko Sumber Rejeki",
          amount: 3200000,
          method: "CREDIT",
          status: "PENDING",
          notes: "30 days credit term",
          createdAt: "2024-01-14T16:45:00Z",
          updatedAt: "2024-01-14T16:45:00Z",
        },
        {
          id: "PAY-004",
          orderId: "ORD-004",
          customerName: "Minimarket Sejahtera",
          amount: 890000,
          method: "E_WALLET",
          status: "COMPLETED",
          reference: "GOPAY-20240114-001",
          createdAt: "2024-01-14T14:20:00Z",
          updatedAt: "2024-01-14T14:25:00Z",
        },
        {
          id: "PAY-005",
          orderId: "ORD-005",
          customerName: "Toko Bahagia",
          amount: 1200000,
          method: "DEBIT_CARD",
          status: "FAILED",
          notes: "Insufficient funds",
          createdAt: "2024-01-13T11:30:00Z",
          updatedAt: "2024-01-13T11:35:00Z",
        },
      ];

      // Apply filters
      let filteredPayments = mockPayments;
      
      if (searchQuery) {
        filteredPayments = filteredPayments.filter(payment =>
          payment.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
          payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          payment.reference?.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      
      if (statusFilter !== "ALL") {
        filteredPayments = filteredPayments.filter(payment => payment.status === statusFilter);
      }
      
      if (methodFilter !== "ALL") {
        filteredPayments = filteredPayments.filter(payment => payment.method === methodFilter);
      }

      setPayments(filteredPayments);
      setTotalItems(filteredPayments.length);
      setTotalPages(Math.ceil(filteredPayments.length / itemsPerPage));
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error("Failed to fetch payments");
    } finally {
      setLoading(false);
    }
  };

  const handleViewPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setShowPaymentModal(true);
  };

  const handleUpdatePaymentStatus = async (paymentId: string, newStatus: string) => {
    try {
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setPayments(prev => 
        prev.map(payment => 
          payment.id === paymentId 
            ? { ...payment, status: newStatus as any, updatedAt: new Date().toISOString() }
            : payment
        )
      );
      
      toast.success("Payment status updated successfully");
    } catch (error) {
      console.error("Error updating payment status:", error);
      toast.error("Failed to update payment status");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: "warning" as const, label: "Pending" },
      COMPLETED: { variant: "success" as const, label: "Completed" },
      FAILED: { variant: "error" as const, label: "Failed" },
      REFUNDED: { variant: "secondary" as const, label: "Refunded" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "default" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getMethodBadge = (method: string) => {
    const methodConfig = {
      CASH: { variant: "success" as const, label: "Cash" },
      BANK_TRANSFER: { variant: "primary" as const, label: "Bank Transfer" },
      CREDIT: { variant: "warning" as const, label: "Credit" },
      DEBIT_CARD: { variant: "info" as const, label: "Debit Card" },
      E_WALLET: { variant: "secondary" as const, label: "E-Wallet" },
    };

    const config = methodConfig[method as keyof typeof methodConfig] || {
      variant: "default" as const,
      label: method,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const statusOptions = [
    { value: "ALL", label: "All Status" },
    { value: "PENDING", label: "Pending" },
    { value: "COMPLETED", label: "Completed" },
    { value: "FAILED", label: "Failed" },
    { value: "REFUNDED", label: "Refunded" },
  ];

  const methodOptions = [
    { value: "ALL", label: "All Methods" },
    { value: "CASH", label: "Cash" },
    { value: "BANK_TRANSFER", label: "Bank Transfer" },
    { value: "CREDIT", label: "Credit" },
    { value: "DEBIT_CARD", label: "Debit Card" },
    { value: "E_WALLET", label: "E-Wallet" },
  ];

  const columns = [
    {
      key: "id",
      title: "Payment ID",
      dataIndex: "id",
      sortable: true,
    },
    {
      key: "orderId",
      title: "Order ID",
      dataIndex: "orderId",
      sortable: true,
    },
    {
      key: "customerName",
      title: "Customer",
      dataIndex: "customerName",
      sortable: true,
    },
    {
      key: "amount",
      title: "Amount",
      dataIndex: "amount",
      render: (value: number) => formatCurrency(value),
      sortable: true,
    },
    {
      key: "method",
      title: "Method",
      dataIndex: "method",
      render: (value: string) => getMethodBadge(value),
    },
    {
      key: "status",
      title: "Status",
      dataIndex: "status",
      render: (value: string) => getStatusBadge(value),
    },
    {
      key: "createdAt",
      title: "Date",
      dataIndex: "createdAt",
      render: (value: string) => formatDate(new Date(value)),
      sortable: true,
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, payment: Payment) => (
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewPayment(payment)}
          >
            View
          </Button>
          {canUpdate && payment.status === "PENDING" && (
            <Select
              options={[
                { value: "COMPLETED", label: "Mark Completed" },
                { value: "FAILED", label: "Mark Failed" },
              ]}
              value=""
              onChange={(value) => handleUpdatePaymentStatus(payment.id, value)}
              placeholder="Update"
              className="w-32"
            />
          )}
        </div>
      ),
    },
  ];

  const statsData = stats ? [
    {
      label: "Total Payments",
      value: stats.totalPayments.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      color: "primary" as const,
    },
    {
      label: "Completed",
      value: stats.completedPayments.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: "success" as const,
    },
    {
      label: "Pending",
      value: stats.pendingPayments.toLocaleString(),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: "warning" as const,
    },
    {
      label: "Total Amount",
      value: formatCurrency(stats.totalAmount),
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      color: "info" as const,
    },
  ] : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Payment Management</h1>
          <p className="text-secondary-600">Track and manage payment transactions</p>
        </div>
      </div>

      {/* Statistics */}
      <Stats stats={statsData} columns={4} />

      {/* Filters */}
      <Card>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <SearchInput
                placeholder="Search payments..."
                value={searchQuery}
                onSearch={setSearchQuery}
                className="max-w-md"
              />
            </div>
            <Select
              options={statusOptions}
              value={statusFilter}
              onChange={setStatusFilter}
              className="w-40"
            />
            <Select
              options={methodOptions}
              value={methodFilter}
              onChange={setMethodFilter}
              className="w-40"
            />
            <Button variant="outline">
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Payments ({totalItems})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" text="Loading payments..." />
            </div>
          ) : payments.length === 0 ? (
            <EmptyState
              title="No payments found"
              description="No payments match your current filters"
            />
          ) : (
            <>
              <Table
                columns={columns}
                data={payments}
                loading={loading}
                onRowClick={handleViewPayment}
              />
              
              <div className="mt-6">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setItemsPerPage}
                  showInfo
                  showSizeChanger
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Payment Details Modal */}
      <Modal
        isOpen={showPaymentModal}
        onClose={() => {
          setShowPaymentModal(false);
          setSelectedPayment(null);
        }}
        title="Payment Details"
        size="md"
      >
        {selectedPayment && (
          <PaymentModal
            payment={selectedPayment}
            onStatusUpdate={handleUpdatePaymentStatus}
            canUpdate={canUpdate}
          />
        )}
      </Modal>
    </div>
  );
};

export default PaymentsPage;
