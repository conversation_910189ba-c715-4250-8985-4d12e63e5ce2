import { AI_CONFIG } from "@/config/constants";
import { FaissStore } from "@langchain/community/vectorstores/faiss";
import { ChatGroq } from "@langchain/groq";
import { OpenAI } from "openai";

// Initialize nvidia chat model
export const chatModelNvidia = new OpenAI({
  apiKey:
    "**********************************************************************",
  baseURL: "https://integrate.api.nvidia.com/v1",
});

// Initialize Groq embeddings
export const embeddings = {
  async embedDocuments(texts: string[]): Promise<number[][]> {
    const response = await chatModelNvidia.embeddings.create({
      model: "nvidia/llama-3.2-nv-embedqa-1b-v2",
      input: texts,
    });
    // Assuming response.data contains embeddings array
    return response.data.map((item: any) => item.embedding);
  },
  async embedQuery(text: string): Promise<number[]> {
    const response = await chatModelNvidia.embeddings.create({
      model: "nvidia/llama-3.2-nv-embedqa-1b-v2",
      input: [text],
    });
    // Assuming response.data[0].embedding contains the embedding
    return response.data[0].embedding;
  },
};

// Initialize Groq chat model
export const chatModel = new ChatGroq({
  apiKey: process.env.GROQ_API_KEY,
  model: "mixtral-8x7b-32768",
  temperature: AI_CONFIG.temperature,
  maxTokens: AI_CONFIG.maxTokens,
});

// Create vector store from texts
export async function createVectorStore(texts: string[], metadata: any[] = []) {
  try {
    return await FaissStore.fromTexts(texts, metadata, embeddings);
  } catch (error) {
    console.error("Error creating vector store:", error);
    throw new Error("Failed to create vector store");
  }
}

// Search for similar documents
export async function searchSimilar(
  vectorStore: FaissStore,
  query: string,
  k = AI_CONFIG.topK
) {
  try {
    const results = await vectorStore.similaritySearch(query, k);
    return results.filter(
      (result) => result.metadata?.score >= AI_CONFIG.searchThreshold
    );
  } catch (error) {
    console.error("Error searching similar documents:", error);
    throw new Error("Failed to search similar documents");
  }
}

// Load existing vector store from directory
export async function loadVectorStore(directory: string) {
  try {
    return await FaissStore.load(directory, embeddings);
  } catch (error) {
    console.error("Error loading vector store:", error);
    throw new Error("Failed to load vector store");
  }
}

// Save vector store to directory
export async function saveVectorStore(
  vectorStore: FaissStore,
  directory: string
) {
  try {
    await vectorStore.save(directory);
  } catch (error) {
    console.error("Error saving vector store:", error);
    throw new Error("Failed to save vector store");
  }
}
