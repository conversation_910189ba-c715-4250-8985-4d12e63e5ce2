"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Badge,
  Button,
  Input,
} from "@/components/ui";
import { formatCurrency, formatDate } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
  warehouse: {
    id: string;
    name: string;
  };
  images?: string[];
  tags?: string[];
  rating?: number;
  reviewCount?: number;
  createdAt: string;
}

interface ProductDetailsModalProps {
  product: Product;
  onClose: () => void;
  onAddToCart?: (product: Product, quantity: number) => void;
  canUpdate: boolean;
}

const ProductDetailsModal: React.FC<ProductDetailsModalProps> = ({
  product,
  onClose,
  onAddToCart,
  canUpdate,
}) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="error">Out of Stock</Badge>;
    } else if (stock <= 10) {
      return <Badge variant="warning">Low Stock ({stock} left)</Badge>;
    } else {
      return <Badge variant="success">In Stock ({stock} available)</Badge>;
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
        </svg>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="w-5 h-5 text-yellow-400" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="half">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="transparent" />
            </linearGradient>
          </defs>
          <path
            fill="url(#half)"
            d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"
          />
        </svg>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-5 h-5 text-secondary-300" viewBox="0 0 20 20">
          <path
            fill="currentColor"
            d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"
          />
        </svg>
      );
    }

    return stars;
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= product.stock) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (onAddToCart) {
      onAddToCart(product, quantity);
      onClose();
    }
  };

  const productImages = product.images && product.images.length > 0 
    ? product.images 
    : ["/api/placeholder/400/400"];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square relative overflow-hidden rounded-lg bg-secondary-50">
            <Image
              src={productImages[selectedImageIndex]}
              alt={product.name}
              fill
              className="object-cover"
            />
          </div>

          {/* Image Thumbnails */}
          {productImages.length > 1 && (
            <div className="flex space-x-2 overflow-x-auto">
              {productImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 w-16 h-16 relative overflow-hidden rounded-lg border-2 transition-colors ${
                    selectedImageIndex === index
                      ? "border-primary-500"
                      : "border-secondary-200 hover:border-secondary-300"
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Information */}
        <div className="space-y-4">
          {/* Category and Stock */}
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">{product.category.name}</Badge>
            {getStockBadge(product.stock)}
          </div>

          {/* Product Name */}
          <h1 className="text-2xl font-bold text-secondary-900">
            {product.name}
          </h1>

          {/* Rating */}
          {product.rating && (
            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                {renderStars(product.rating)}
              </div>
              <span className="text-secondary-600">
                {product.rating} ({product.reviewCount || 0} reviews)
              </span>
            </div>
          )}

          {/* Price */}
          <div className="text-3xl font-bold text-primary-600">
            {formatCurrency(product.price)}
          </div>

          {/* Description */}
          {product.description && (
            <div>
              <h3 className="font-semibold text-secondary-900 mb-2">Description</h3>
              <p className="text-secondary-700">{product.description}</p>
            </div>
          )}

          {/* Tags */}
          {product.tags && product.tags.length > 0 && (
            <div>
              <h3 className="font-semibold text-secondary-900 mb-2">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 text-sm bg-secondary-100 text-secondary-700 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Quantity Selector and Add to Cart */}
          {onAddToCart && product.stock > 0 && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-2">
                  Quantity
                </label>
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    -
                  </Button>
                  <Input
                    type="number"
                    value={quantity}
                    onChange={(e) => handleQuantityChange(Number(e.target.value))}
                    className="w-20 text-center"
                    min="1"
                    max={product.stock}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= product.stock}
                  >
                    +
                  </Button>
                </div>
              </div>

              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={handleAddToCart}
              >
                Add {quantity} to Cart - {formatCurrency(product.price * quantity)}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Product Details */}
        <Card>
          <CardHeader>
            <CardTitle>Product Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-secondary-600">Product ID:</span>
                <span className="font-medium">{product.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Category:</span>
                <span className="font-medium">{product.category.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Warehouse:</span>
                <span className="font-medium">{product.warehouse.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Stock:</span>
                <span className="font-medium">{product.stock} units</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Date Added:</span>
                <span className="font-medium">{formatDate(new Date(product.createdAt))}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {canUpdate && (
                <>
                  <Button variant="outline" className="w-full">
                    Edit Product
                  </Button>
                  <Button variant="outline" className="w-full">
                    Update Stock
                  </Button>
                  <Button variant="outline" className="w-full">
                    View Analytics
                  </Button>
                </>
              )}
              <Button variant="outline" className="w-full">
                Share Product
              </Button>
              <Button variant="outline" className="w-full">
                Print Label
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Close Button */}
      <div className="flex justify-end">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  );
};

export default ProductDetailsModal;
