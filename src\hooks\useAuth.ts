import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { hasPermission, hasAnyPermission, canAccessResource } from "@/lib/auth";
import { USER_ROLES } from "@/config/constants";

// Custom hook for authentication
export function useAuth() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const user = session?.user;
  const isLoading = status === "loading";
  const isAuthenticated = status === "authenticated";

  // Redirect to login if not authenticated
  const requireAuth = (redirectTo = "/auth/signin") => {
    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push(redirectTo);
      }
    }, [isLoading, isAuthenticated, redirectTo]);
  };

  // Check if user has specific permission
  const checkPermission = (permission: string): boolean => {
    if (!user?.permissions) return false;
    return hasPermission(user.permissions, permission);
  };

  // Check if user has any of the permissions
  const checkAnyPermission = (permissions: string[]): boolean => {
    if (!user?.permissions) return false;
    return hasAnyPermission(user.permissions, permissions);
  };

  // Check if user has specific role
  const checkRole = (role: string): boolean => {
    return user?.role === role;
  };

  // Check if user has any of the roles
  const checkAnyRole = (roles: string[]): boolean => {
    if (!user?.role) return false;
    return roles.includes(user.role);
  };

  // Check if user can access resource
  const checkAccess = (requiredRoles: string[]): boolean => {
    if (!user?.role) return false;
    return canAccessResource(user.role, requiredRoles);
  };

  // Role-specific checks
  const isOwner = checkRole(USER_ROLES.OWNER);
  const isAdmin = checkRole(USER_ROLES.ADMIN) || isOwner;
  const isSales = checkRole(USER_ROLES.SALES) || isAdmin;
  const isCashier = checkRole(USER_ROLES.CASHIER) || isAdmin;
  const isCustomer = checkRole(USER_ROLES.CUSTOMER);

  // Permission-specific checks
  const canManageProducts = checkAnyPermission([
    "product:create",
    "product:update",
    "product:delete",
  ]);

  const canManageOrders = checkAnyPermission([
    "order:create",
    "order:update",
    "order:delete",
  ]);

  const canManageCustomers = checkAnyPermission([
    "customer:create",
    "customer:update",
    "customer:delete",
  ]);

  const canManageUsers = checkAnyPermission([
    "user:create",
    "user:update",
    "user:delete",
  ]);

  const canViewReports = checkPermission("reports:read");
  const canManageInventory = checkPermission("inventory:update");
  const canManageSystem = checkPermission("system:config");

  return {
    // Session data
    user,
    session,
    isLoading,
    isAuthenticated,

    // Utility functions
    requireAuth,
    checkPermission,
    checkAnyPermission,
    checkRole,
    checkAnyRole,
    checkAccess,

    // Role checks
    isOwner,
    isAdmin,
    isSales,
    isCashier,
    isCustomer,

    // Permission checks
    canManageProducts,
    canManageOrders,
    canManageCustomers,
    canManageUsers,
    canViewReports,
    canManageInventory,
    canManageSystem,
  };
}

// Hook for requiring specific permission
export function useRequirePermission(permission: string) {
  const { checkPermission, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && !checkPermission(permission)) {
      router.push("/dashboard?error=insufficient-permissions");
    }
  }, [isLoading, isAuthenticated, permission, checkPermission, router]);

  return checkPermission(permission);
}

// Hook for requiring specific role
export function useRequireRole(role: string) {
  const { checkRole, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && !checkRole(role)) {
      router.push("/dashboard?error=insufficient-role");
    }
  }, [isLoading, isAuthenticated, role, checkRole, router]);

  return checkRole(role);
}

// Hook for requiring any of the roles
export function useRequireAnyRole(roles: string[]) {
  const { checkAnyRole, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && !checkAnyRole(roles)) {
      router.push("/dashboard?error=insufficient-role");
    }
  }, [isLoading, isAuthenticated, roles, checkAnyRole, router]);

  return checkAnyRole(roles);
}

// Hook for admin-only access
export function useRequireAdmin() {
  return useRequireAnyRole([USER_ROLES.OWNER, USER_ROLES.ADMIN]);
}

// Hook for owner-only access
export function useRequireOwner() {
  return useRequireRole(USER_ROLES.OWNER);
}

// Hook for staff access (admin, sales, cashier)
export function useRequireStaff() {
  return useRequireAnyRole([
    USER_ROLES.OWNER,
    USER_ROLES.ADMIN,
    USER_ROLES.SALES,
    USER_ROLES.CASHIER,
  ]);
}
