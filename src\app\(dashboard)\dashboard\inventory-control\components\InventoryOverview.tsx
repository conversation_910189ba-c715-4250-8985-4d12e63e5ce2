"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  Table,
  Badge,
  <PERSON><PERSON>,
  LoadingSpinner,
} from "@/components/ui";
import { formatCurrency } from "@/lib/utils";

interface InventoryStats {
  totalProducts: number;
  totalValue: number;
  lowStockItems: number;
  outOfStockItems: number;
  reorderRequired: number;
  averageTurnover: number;
  monthlyMovements: number;
  warehouseCount: number;
}

interface TopProduct {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  value: number;
  turnoverRate: number;
  status: "HEALTHY" | "LOW_STOCK" | "OUT_OF_STOCK" | "OVERSTOCK";
}

interface WarehouseStatus {
  id: string;
  name: string;
  totalProducts: number;
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
  utilizationRate: number;
}

interface InventoryOverviewProps {
  stats: InventoryStats | null;
  period: string;
  warehouse: string;
  onStockAdjustment: (product: any) => void;
}

const InventoryOverview: React.FC<InventoryOverviewProps> = ({
  stats,
  period,
  warehouse,
  onStockAdjustment,
}) => {
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [warehouseStatus, setWarehouseStatus] = useState<WarehouseStatus[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOverviewData();
  }, [period, warehouse]);

  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - replace with actual API endpoint
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock top products data
      setTopProducts([
        {
          id: "1",
          name: "Indomie Goreng Original",
          category: "Makanan Instan",
          currentStock: 450,
          value: 1350000,
          turnoverRate: 8.5,
          status: "HEALTHY",
        },
        {
          id: "2",
          name: "Teh Botol Sosro 450ml",
          category: "Minuman",
          currentStock: 5,
          value: 22500,
          turnoverRate: 12.3,
          status: "LOW_STOCK",
        },
        {
          id: "3",
          name: "Chitato Sapi Panggang",
          category: "Makanan Ringan",
          currentStock: 0,
          value: 0,
          turnoverRate: 6.7,
          status: "OUT_OF_STOCK",
        },
        {
          id: "4",
          name: "Beras Premium 5kg",
          category: "Bahan Pokok",
          currentStock: 85,
          value: 6375000,
          turnoverRate: 3.2,
          status: "HEALTHY",
        },
        {
          id: "5",
          name: "Minyak Goreng Tropical",
          category: "Bahan Pokok",
          currentStock: 250,
          value: 4500000,
          turnoverRate: 1.8,
          status: "OVERSTOCK",
        },
      ]);

      // Mock warehouse status data
      setWarehouseStatus([
        {
          id: "1",
          name: "Gudang Utama",
          totalProducts: 89,
          totalValue: 156780000,
          lowStockCount: 15,
          outOfStockCount: 5,
          utilizationRate: 78.5,
        },
        {
          id: "2",
          name: "Gudang Cabang",
          totalProducts: 45,
          totalValue: 67890000,
          lowStockCount: 6,
          outOfStockCount: 2,
          utilizationRate: 65.2,
        },
        {
          id: "3",
          name: "Gudang Frozen",
          totalProducts: 22,
          totalValue: 21010000,
          lowStockCount: 2,
          outOfStockCount: 1,
          utilizationRate: 45.8,
        },
      ]);
    } catch (error) {
      console.error("Error fetching overview data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      HEALTHY: { variant: "success" as const, label: "Healthy" },
      LOW_STOCK: { variant: "warning" as const, label: "Low Stock" },
      OUT_OF_STOCK: { variant: "error" as const, label: "Out of Stock" },
      OVERSTOCK: { variant: "info" as const, label: "Overstock" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "default" as const,
      label: status,
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const productColumns = [
    {
      key: "name",
      title: "Product",
      dataIndex: "name",
    },
    {
      key: "category",
      title: "Category",
      dataIndex: "category",
    },
    {
      key: "currentStock",
      title: "Stock",
      dataIndex: "currentStock",
      render: (value: number) => value.toLocaleString(),
    },
    {
      key: "value",
      title: "Value",
      dataIndex: "value",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "turnoverRate",
      title: "Turnover Rate",
      dataIndex: "turnoverRate",
      render: (value: number) => `${value}x/month`,
    },
    {
      key: "status",
      title: "Status",
      dataIndex: "status",
      render: (value: string) => getStatusBadge(value),
    },
    {
      key: "actions",
      title: "Actions",
      render: (_: any, product: TopProduct) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onStockAdjustment(product)}
          disabled={product.status === "HEALTHY"}
        >
          Adjust
        </Button>
      ),
    },
  ];

  const warehouseColumns = [
    {
      key: "name",
      title: "Warehouse",
      dataIndex: "name",
    },
    {
      key: "totalProducts",
      title: "Products",
      dataIndex: "totalProducts",
      render: (value: number) => value.toLocaleString(),
    },
    {
      key: "totalValue",
      title: "Total Value",
      dataIndex: "totalValue",
      render: (value: number) => formatCurrency(value),
    },
    {
      key: "alerts",
      title: "Alerts",
      render: (_: any, warehouse: WarehouseStatus) => (
        <div className="flex space-x-1">
          {warehouse.lowStockCount > 0 && (
            <Badge variant="warning" size="sm">
              {warehouse.lowStockCount} Low
            </Badge>
          )}
          {warehouse.outOfStockCount > 0 && (
            <Badge variant="error" size="sm">
              {warehouse.outOfStockCount} Out
            </Badge>
          )}
          {warehouse.lowStockCount === 0 && warehouse.outOfStockCount === 0 && (
            <Badge variant="success" size="sm">
              All Good
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "utilizationRate",
      title: "Utilization",
      dataIndex: "utilizationRate",
      render: (value: number) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-secondary-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${
                value >= 80 ? "bg-success-500" :
                value >= 60 ? "bg-warning-500" : "bg-error-500"
              }`}
              style={{ width: `${Math.min(value, 100)}%` }}
            />
          </div>
          <span className="text-sm">{value}%</span>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" text="Loading overview..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Inventory Health Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Inventory Health</p>
                <p className="text-2xl font-bold text-success-600">
                  {stats ? Math.round(((stats.totalProducts - stats.lowStockItems - stats.outOfStockItems) / stats.totalProducts) * 100) : 0}%
                </p>
              </div>
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Avg. Turnover Rate</p>
                <p className="text-2xl font-bold text-primary-600">
                  {stats?.averageTurnover || 0}x
                </p>
                <p className="text-xs text-secondary-500">per month</p>
              </div>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Reorder Required</p>
                <p className="text-2xl font-bold text-warning-600">
                  {stats?.reorderRequired || 0}
                </p>
                <p className="text-xs text-secondary-500">products</p>
              </div>
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Products by Value */}
      <Card>
        <CardHeader>
          <CardTitle>Top Products by Value</CardTitle>
        </CardHeader>
        <CardContent>
          <Table
            columns={productColumns}
            data={topProducts}
            emptyText="No products data available"
          />
        </CardContent>
      </Card>

      {/* Warehouse Status */}
      <Card>
        <CardHeader>
          <CardTitle>Warehouse Status</CardTitle>
        </CardHeader>
        <CardContent>
          <Table
            columns={warehouseColumns}
            data={warehouseStatus}
            emptyText="No warehouse data available"
          />
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h3 className="font-medium text-secondary-900">Add Stock</h3>
            <p className="text-sm text-secondary-600">Receive new inventory</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="font-medium text-secondary-900">Stock Audit</h3>
            <p className="text-sm text-secondary-600">Perform stock count</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 className="font-medium text-secondary-900">Generate Report</h3>
            <p className="text-sm text-secondary-600">Export inventory data</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-info-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-info-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </div>
            <h3 className="font-medium text-secondary-900">Transfer Stock</h3>
            <p className="text-sm text-secondary-600">Move between warehouses</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default InventoryOverview;
