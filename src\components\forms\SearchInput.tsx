import React, { useState, useCallback } from "react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { debounce } from "@/lib/utils";

export interface SearchInputProps {
  placeholder?: string;
  value?: string;
  onSearch: (query: string) => void;
  onClear?: () => void;
  loading?: boolean;
  debounceMs?: number;
  showSearchButton?: boolean;
  className?: string;
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = "Search...",
  value = "",
  onSearch,
  onClear,
  loading = false,
  debounceMs = 300,
  showSearchButton = false,
  className,
}) => {
  const [searchValue, setSearchValue] = useState(value);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      onSearch(query);
    }, debounceMs),
    [onSearch, debounceMs]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchValue(newValue);
    
    if (!showSearchButton) {
      debouncedSearch(newValue);
    }
  };

  const handleSearch = () => {
    onSearch(searchValue);
  };

  const handleClear = () => {
    setSearchValue("");
    onSearch("");
    onClear?.();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && showSearchButton) {
      handleSearch();
    }
  };

  const searchIcon = (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
  );

  const clearIcon = searchValue && (
    <button
      type="button"
      onClick={handleClear}
      className="text-secondary-400 hover:text-secondary-600 transition-colors"
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </button>
  );

  if (showSearchButton) {
    return (
      <div className={`flex space-x-2 ${className}`}>
        <Input
          type="text"
          placeholder={placeholder}
          value={searchValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          leftIcon={searchIcon}
          rightIcon={clearIcon}
          className="flex-1"
        />
        <Button
          onClick={handleSearch}
          loading={loading}
          disabled={!searchValue.trim()}
        >
          Search
        </Button>
      </div>
    );
  }

  return (
    <Input
      type="text"
      placeholder={placeholder}
      value={searchValue}
      onChange={handleInputChange}
      leftIcon={loading ? (
        <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      ) : searchIcon}
      rightIcon={clearIcon}
      className={className}
    />
  );
};

export { SearchInput };
