import prisma from "@/db/client";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { PromptTemplate } from "@langchain/core/prompts";
import { chatModel } from "./config";

export class AIInventoryManager {
  // Analyze inventory levels and generate insights
  async analyzeInventoryLevels() {
    try {
      const products = await prisma.product.findMany({
        include: {
          category: true,
          warehouse: true,
          inventoryTransactions: {
            orderBy: { createdAt: "desc" },
            take: 10,
          },
        },
      });

      const lowStockProducts = products.filter((p) => p.stock <= 10);
      const outOfStockProducts = products.filter((p) => p.stock === 0);
      const overstockProducts = products.filter((p) => p.stock > 100);

      const analysis = {
        totalProducts: products.length,
        lowStock: lowStockProducts.length,
        outOfStock: outOfStockProducts.length,
        overstock: overstockProducts.length,
        lowStockProducts: lowStockProducts.map((p) => ({
          id: p.id,
          name: p.name,
          stock: p.stock,
          category: p.category.name,
          warehouse: p.warehouse.name,
        })),
        outOfStockProducts: outOfStockProducts.map((p) => ({
          id: p.id,
          name: p.name,
          category: p.category.name,
          warehouse: p.warehouse.name,
        })),
      };

      return analysis;
    } catch (error) {
      console.error("Error analyzing inventory levels:", error);
      throw new Error("Failed to analyze inventory levels");
    }
  }

  // Generate AI-powered reorder recommendations
  async generateReorderRecommendations() {
    try {
      const inventoryAnalysis = await this.analyzeInventoryLevels();

      if (inventoryAnalysis.lowStockProducts.length === 0) {
        return {
          recommendations: [],
          message: "All products have sufficient stock levels",
        };
      }

      const prompt = PromptTemplate.fromTemplate(`
        As an AI inventory manager for Arta Boga distributor, analyze the following low stock products and provide reorder recommendations:

        Low Stock Products:
        {lowStockProducts}

        Please provide:
        1. Recommended reorder quantities for each product
        2. Priority level (High, Medium, Low)
        3. Reasoning for each recommendation
        4. Estimated reorder cost

        Format your response as a structured analysis with actionable recommendations.
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());

      const aiRecommendations = await chain.invoke({
        lowStockProducts: JSON.stringify(
          inventoryAnalysis.lowStockProducts,
          null,
          2
        ),
      });

      return {
        recommendations: aiRecommendations,
        lowStockCount: inventoryAnalysis.lowStock,
        outOfStockCount: inventoryAnalysis.outOfStock,
        analysis: inventoryAnalysis,
      };
    } catch (error) {
      console.error("Error generating reorder recommendations:", error);
      throw new Error("Failed to generate reorder recommendations");
    }
  }

  // Predict demand based on historical data
  async predictDemand(productId: string, days = 30) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          category: true,
          orderItems: {
            include: {
              order: true,
            },
            where: {
              order: {
                createdAt: {
                  gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
                },
              },
            },
          },
        },
      });

      if (!product) {
        throw new Error("Product not found");
      }

      const totalSold = product.orderItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      const averageDailyDemand = totalSold / days;
      const predictedDemand = Math.ceil(averageDailyDemand * 30); // 30-day prediction

      // Get seasonal trends
      const monthlyData = await this.getMonthlyDemandData(productId);

      const prompt = PromptTemplate.fromTemplate(`
        Analyze the demand pattern for this product and provide insights:

        Product: {productName}
        Category: {category}
        Current Stock: {currentStock}
        Total Sold (last {days} days): {totalSold}
        Average Daily Demand: {averageDailyDemand}
        Predicted 30-day Demand: {predictedDemand}
        Monthly Data: {monthlyData}

        Provide:
        1. Demand trend analysis
        2. Seasonal patterns if any
        3. Recommended safety stock level
        4. Optimal reorder point
        5. Risk assessment
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());

      const analysis = await chain.invoke({
        productName: product.name,
        category: product.category.name,
        currentStock: product.stock.toString(),
        totalSold: totalSold.toString(),
        averageDailyDemand: averageDailyDemand.toFixed(2),
        predictedDemand: predictedDemand.toString(),
        days: days.toString(),
        monthlyData: JSON.stringify(monthlyData),
      });

      return {
        productId,
        productName: product.name,
        currentStock: product.stock,
        totalSold,
        averageDailyDemand: parseFloat(averageDailyDemand.toFixed(2)),
        predictedDemand,
        analysis,
        monthlyData,
      };
    } catch (error) {
      console.error("Error predicting demand:", error);
      throw new Error("Failed to predict demand");
    }
  }

  // Get monthly demand data for trend analysis
  async getMonthlyDemandData(productId: string) {
    try {
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const orderItems = await prisma.orderItem.findMany({
        where: {
          productId,
          order: {
            createdAt: {
              gte: sixMonthsAgo,
            },
          },
        },
        include: {
          order: true,
        },
      });

      const monthlyData: { [key: string]: number } = {};

      orderItems.forEach((item) => {
        const month = item.order.createdAt.toISOString().substring(0, 7); // YYYY-MM
        monthlyData[month] = (monthlyData[month] || 0) + item.quantity;
      });

      return monthlyData;
    } catch (error) {
      console.error("Error getting monthly demand data:", error);
      return {};
    }
  }

  // Generate inventory alerts
  async generateInventoryAlerts() {
    try {
      const analysis = await this.analyzeInventoryLevels();
      const alerts = [];

      // Critical stock alerts
      if (analysis.outOfStockProducts.length > 0) {
        alerts.push({
          type: "CRITICAL",
          title: "Out of Stock Alert",
          message: `${analysis.outOfStockProducts.length} products are out of stock`,
          products: analysis.outOfStockProducts,
          priority: "HIGH",
        });
      }

      // Low stock alerts
      if (analysis.lowStockProducts.length > 0) {
        alerts.push({
          type: "WARNING",
          title: "Low Stock Alert",
          message: `${analysis.lowStockProducts.length} products have low stock levels`,
          products: analysis.lowStockProducts,
          priority: "MEDIUM",
        });
      }

      // Create notifications in database
      for (const alert of alerts) {
        await prisma.notification.create({
          data: {
            title: alert.title,
            message: alert.message,
            type: alert.type === "CRITICAL" ? "ERROR" : "WARNING",
            priority: alert.priority === "HIGH" ? "HIGH" : "MEDIUM",
            metadata: {
              products: alert.products,
              alertType: "INVENTORY",
            },
          },
        });
      }

      return alerts;
    } catch (error) {
      console.error("Error generating inventory alerts:", error);
      throw new Error("Failed to generate inventory alerts");
    }
  }

  // Optimize warehouse allocation
  async optimizeWarehouseAllocation() {
    try {
      const warehouses = await prisma.warehouse.findMany({
        include: {
          products: {
            include: {
              category: true,
              orderItems: {
                include: {
                  order: true,
                },
                where: {
                  order: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
                    },
                  },
                },
              },
            },
          },
        },
      });

      const warehouseAnalysis = warehouses.map((warehouse) => {
        const totalProducts = warehouse.products.length;
        const totalStock = warehouse.products.reduce(
          (sum, p) => sum + p.stock,
          0
        );
        const totalSales = warehouse.products.reduce(
          (sum, p) =>
            sum +
            p.orderItems.reduce((itemSum, item) => itemSum + item.quantity, 0),
          0
        );
        const turnoverRate = totalStock > 0 ? totalSales / totalStock : 0;

        return {
          id: warehouse.id,
          name: warehouse.name,
          address: warehouse.address,
          totalProducts,
          totalStock,
          totalSales,
          turnoverRate: parseFloat(turnoverRate.toFixed(2)),
          efficiency:
            turnoverRate > 0.5 ? "High" : turnoverRate > 0.2 ? "Medium" : "Low",
        };
      });

      const prompt = PromptTemplate.fromTemplate(`
        Analyze the warehouse performance data and provide optimization recommendations:

        Warehouse Analysis:
        {warehouseData}

        Provide:
        1. Performance assessment for each warehouse
        2. Recommendations for product redistribution
        3. Efficiency improvement suggestions
        4. Cost optimization opportunities
        5. Strategic recommendations for inventory allocation
      `);

      const chain = prompt.pipe(chatModel).pipe(new StringOutputParser());

      const recommendations = await chain.invoke({
        warehouseData: JSON.stringify(warehouseAnalysis, null, 2),
      });

      return {
        warehouseAnalysis,
        recommendations,
        totalWarehouses: warehouses.length,
        averageTurnover:
          warehouseAnalysis.reduce((sum, w) => sum + w.turnoverRate, 0) /
          warehouses.length,
      };
    } catch (error) {
      console.error("Error optimizing warehouse allocation:", error);
      throw new Error("Failed to optimize warehouse allocation");
    }
  }
}

// Export singleton instance
export const aiInventoryManager = new AIInventoryManager();
